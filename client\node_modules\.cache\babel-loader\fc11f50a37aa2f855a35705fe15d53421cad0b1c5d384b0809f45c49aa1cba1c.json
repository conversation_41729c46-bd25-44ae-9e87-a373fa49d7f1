{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst BookMarked = createLucideIcon(\"BookMarked\", [[\"path\", {\n  d: \"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20\",\n  key: \"t4utmx\"\n}], [\"polyline\", {\n  points: \"10 2 10 10 13 7 16 10 16 2\",\n  key: \"13o6vz\"\n}]]);\nexport { BookMarked as default };\n//# sourceMappingURL=book-marked.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
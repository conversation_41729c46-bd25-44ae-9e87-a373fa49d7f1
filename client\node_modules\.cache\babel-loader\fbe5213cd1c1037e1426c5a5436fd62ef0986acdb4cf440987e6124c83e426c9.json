{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Plus,RefreshCw,CheckCircle,XCircle,AlertTriangle,Clock,Activity,Search,Filter,Eye,StopCircle}from'lucide-react';import{finetuneAPI}from'../services/api';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Batches=()=>{const[batches,setBatches]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[searchTerm,setSearchTerm]=useState('');const[statusFilter,setStatusFilter]=useState('all');useEffect(()=>{fetchBatches();},[]);const fetchBatches=async()=>{try{setLoading(true);setError(null);const response=await finetuneAPI.getBatches();setBatches(response.data||[]);}catch(err){setError('Failed to fetch batch jobs');console.error('Error fetching batches:',err);}finally{setLoading(false);}};const handleCancelBatch=async batchId=>{if(!window.confirm('Are you sure you want to cancel this batch job?')){return;}try{await finetuneAPI.cancelBatch(batchId);await fetchBatches();}catch(err){var _err$response,_err$response$data,_err$response$data$er;console.error('Error cancelling batch:',err);alert('Failed to cancel batch: '+(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:(_err$response$data$er=_err$response$data.error)===null||_err$response$data$er===void 0?void 0:_err$response$data$er.message)||err.message));}};const getStatusIcon=status=>{switch(status){case'COMPLETED':return/*#__PURE__*/_jsx(CheckCircle,{className:\"h-4 w-4 text-green-500\"});case'IN_PROGRESS':return/*#__PURE__*/_jsx(Activity,{className:\"h-4 w-4 text-blue-500\"});case'VALIDATING':return/*#__PURE__*/_jsx(Clock,{className:\"h-4 w-4 text-yellow-500\"});case'FAILED':return/*#__PURE__*/_jsx(XCircle,{className:\"h-4 w-4 text-red-500\"});case'EXPIRED':return/*#__PURE__*/_jsx(AlertTriangle,{className:\"h-4 w-4 text-orange-500\"});case'CANCELLED':return/*#__PURE__*/_jsx(StopCircle,{className:\"h-4 w-4 text-gray-500\"});default:return/*#__PURE__*/_jsx(AlertTriangle,{className:\"h-4 w-4 text-gray-500\"});}};const getStatusClass=status=>{switch(status){case'COMPLETED':return'bg-green-100 text-green-800';case'IN_PROGRESS':return'bg-blue-100 text-blue-800';case'VALIDATING':return'bg-yellow-100 text-yellow-800';case'FAILED':return'bg-red-100 text-red-800';case'EXPIRED':return'bg-orange-100 text-orange-800';case'CANCELLED':return'bg-gray-100 text-gray-800';default:return'bg-gray-100 text-gray-800';}};const filteredBatches=batches.filter(batch=>{const matchesSearch=batch.id.toLowerCase().includes(searchTerm.toLowerCase())||batch.model_id&&batch.model_id.toLowerCase().includes(searchTerm.toLowerCase());const matchesStatus=statusFilter==='all'||batch.status===statusFilter;return matchesSearch&&matchesStatus;});if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-64\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Loading batch jobs...\"})]})});}if(error){return/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(XCircle,{className:\"h-12 w-12 text-red-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-2\",children:\"Error Loading Batches\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-4\",children:error}),/*#__PURE__*/_jsxs(\"button\",{onClick:fetchBatches,className:\"btn-primary\",children:[/*#__PURE__*/_jsx(RefreshCw,{className:\"h-5 w-5 mr-2\"}),\"Retry\"]})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"Batch Jobs\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mt-1\",children:\"Manage your batch processing jobs\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-3\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:fetchBatches,className:\"btn-secondary\",children:[/*#__PURE__*/_jsx(RefreshCw,{className:\"h-5 w-5 mr-2\"}),\"Refresh\"]}),/*#__PURE__*/_jsxs(\"button\",{className:\"btn-primary\",children:[/*#__PURE__*/_jsx(Plus,{className:\"h-5 w-5 mr-2\"}),\"Create Batch\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-4 gap-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-600\",children:\"Total Batches\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-gray-900\",children:batches.length})]}),/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-blue-100 rounded-lg\",children:/*#__PURE__*/_jsx(Activity,{className:\"h-6 w-6 text-blue-600\"})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"card\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-600\",children:\"Completed\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-green-600\",children:batches.filter(b=>b.status==='COMPLETED').length})]}),/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-green-100 rounded-lg\",children:/*#__PURE__*/_jsx(CheckCircle,{className:\"h-6 w-6 text-green-600\"})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"card\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-600\",children:\"In Progress\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-blue-600\",children:batches.filter(b=>b.status==='IN_PROGRESS').length})]}),/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-blue-100 rounded-lg\",children:/*#__PURE__*/_jsx(Activity,{className:\"h-6 w-6 text-blue-600\"})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"card\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-600\",children:\"Failed\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-red-600\",children:batches.filter(b=>b.status==='FAILED').length})]}),/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-red-100 rounded-lg\",children:/*#__PURE__*/_jsx(XCircle,{className:\"h-6 w-6 text-red-600\"})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"card\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 sm:space-x-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative flex-1\",children:[/*#__PURE__*/_jsx(Search,{className:\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search batch jobs...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),className:\"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(Filter,{className:\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"}),/*#__PURE__*/_jsxs(\"select\",{value:statusFilter,onChange:e=>setStatusFilter(e.target.value),className:\"pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none bg-white min-w-[150px]\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"VALIDATING\",children:\"Validating\"}),/*#__PURE__*/_jsx(\"option\",{value:\"IN_PROGRESS\",children:\"In Progress\"}),/*#__PURE__*/_jsx(\"option\",{value:\"COMPLETED\",children:\"Completed\"}),/*#__PURE__*/_jsx(\"option\",{value:\"FAILED\",children:\"Failed\"}),/*#__PURE__*/_jsx(\"option\",{value:\"EXPIRED\",children:\"Expired\"}),/*#__PURE__*/_jsx(\"option\",{value:\"CANCELLED\",children:\"Cancelled\"})]})]})]})}),filteredBatches.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(Activity,{className:\"h-12 w-12 text-gray-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-2\",children:\"No Batch Jobs Found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Create your first batch job to get started\"})]}):/*#__PURE__*/_jsx(\"div\",{className:\"card overflow-hidden\",children:/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Model\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Progress\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Created\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:filteredBatches.map(batch=>/*#__PURE__*/_jsxs(\"tr\",{className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsxs(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900\",children:[batch.id.substring(0,8),\"...\"]}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:batch.model_id?batch.model_id.split('/').pop():'N/A'}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"span\",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusClass(batch.status)}`,children:[getStatusIcon(batch.status),/*#__PURE__*/_jsx(\"span\",{className:\"ml-1\",children:batch.status})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:batch.progress!==undefined?`${batch.progress}%`:'N/A'}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",children:new Date(batch.created_at).toLocaleDateString()}),/*#__PURE__*/_jsxs(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"text-primary-600 hover:text-primary-900\",children:/*#__PURE__*/_jsx(Eye,{className:\"h-4 w-4\"})}),['IN_PROGRESS','VALIDATING'].includes(batch.status)&&/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleCancelBatch(batch.id),className:\"text-red-600 hover:text-red-900\",children:/*#__PURE__*/_jsx(StopCircle,{className:\"h-4 w-4\"})})]})]},batch.id))})]})})})]});};export default Batches;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"version": 3, "file": "workbox-broadcast-update.dev.js", "sources": ["../_version.js", "../responsesAreSame.js", "../utils/constants.js", "../BroadcastCacheUpdate.js", "../BroadcastUpdatePlugin.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:broadcast-update:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport './_version.js';\n/**\n * Given two `Response's`, compares several header values to see if they are\n * the same or not.\n *\n * @param {Response} firstResponse\n * @param {Response} secondResponse\n * @param {Array<string>} headersToCheck\n * @return {boolean}\n *\n * @memberof workbox-broadcast-update\n */\nconst responsesAreSame = (firstResponse, secondResponse, headersToCheck) => {\n    if (process.env.NODE_ENV !== 'production') {\n        if (!(firstResponse instanceof Response && secondResponse instanceof Response)) {\n            throw new WorkboxError('invalid-responses-are-same-args');\n        }\n    }\n    const atLeastOneHeaderAvailable = headersToCheck.some((header) => {\n        return (firstResponse.headers.has(header) && secondResponse.headers.has(header));\n    });\n    if (!atLeastOneHeaderAvailable) {\n        if (process.env.NODE_ENV !== 'production') {\n            logger.warn(`Unable to determine where the response has been updated ` +\n                `because none of the headers that would be checked are present.`);\n            logger.debug(`Attempting to compare the following: `, firstResponse, secondResponse, headersToCheck);\n        }\n        // Just return true, indicating the that responses are the same, since we\n        // can't determine otherwise.\n        return true;\n    }\n    return headersToCheck.every((header) => {\n        const headerStateComparison = firstResponse.headers.has(header) === secondResponse.headers.has(header);\n        const headerValueComparison = firstResponse.headers.get(header) === secondResponse.headers.get(header);\n        return headerStateComparison && headerValueComparison;\n    });\n};\nexport { responsesAreSame };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nexport const CACHE_UPDATED_MESSAGE_TYPE = 'CACHE_UPDATED';\nexport const CACHE_UPDATED_MESSAGE_META = 'workbox-broadcast-update';\nexport const NOTIFY_ALL_CLIENTS = true;\nexport const DEFAULT_HEADERS_TO_CHECK = [\n    'content-length',\n    'etag',\n    'last-modified',\n];\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { timeout } from 'workbox-core/_private/timeout.js';\nimport { resultingClientExists } from 'workbox-core/_private/resultingClientExists.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { responsesAreSame } from './responsesAreSame.js';\nimport { CACHE_UPDATED_MESSAGE_META, CACHE_UPDATED_MESSAGE_TYPE, DEFAULT_HEADERS_TO_CHECK, NOTIFY_ALL_CLIENTS, } from './utils/constants.js';\nimport './_version.js';\n// UA-sniff Safari: https://stackoverflow.com/questions/7944460/detect-safari-browser\n// TODO(phil<PERSON><PERSON><PERSON>): remove once this Safari bug fix has been released.\n// https://bugs.webkit.org/show_bug.cgi?id=201169\nconst isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n/**\n * Generates the default payload used in update messages. By default the\n * payload includes the `cacheName` and `updatedURL` fields.\n *\n * @return Object\n * @private\n */\nfunction defaultPayloadGenerator(data) {\n    return {\n        cacheName: data.cacheName,\n        updatedURL: data.request.url,\n    };\n}\n/**\n * Uses the `postMessage()` API to inform any open windows/tabs when a cached\n * response has been updated.\n *\n * For efficiency's sake, the underlying response bodies are not compared;\n * only specific response headers are checked.\n *\n * @memberof workbox-broadcast-update\n */\nclass BroadcastCacheUpdate {\n    /**\n     * Construct a BroadcastCacheUpdate instance with a specific `channelName` to\n     * broadcast messages on\n     *\n     * @param {Object} [options]\n     * @param {Array<string>} [options.headersToCheck=['content-length', 'etag', 'last-modified']]\n     *     A list of headers that will be used to determine whether the responses\n     *     differ.\n     * @param {string} [options.generatePayload] A function whose return value\n     *     will be used as the `payload` field in any cache update messages sent\n     *     to the window clients.\n     * @param {boolean} [options.notifyAllClients=true] If true (the default) then\n     *     all open clients will receive a message. If false, then only the client\n     *     that make the original request will be notified of the update.\n     */\n    constructor({ generatePayload, headersToCheck, notifyAllClients, } = {}) {\n        this._headersToCheck = headersToCheck || DEFAULT_HEADERS_TO_CHECK;\n        this._generatePayload = generatePayload || defaultPayloadGenerator;\n        this._notifyAllClients = notifyAllClients !== null && notifyAllClients !== void 0 ? notifyAllClients : NOTIFY_ALL_CLIENTS;\n    }\n    /**\n     * Compares two [Responses](https://developer.mozilla.org/en-US/docs/Web/API/Response)\n     * and sends a message (via `postMessage()`) to all window clients if the\n     * responses differ. Neither of the Responses can be\n     * [opaque](https://developer.chrome.com/docs/workbox/caching-resources-during-runtime/#opaque-responses).\n     *\n     * The message that's posted has the following format (where `payload` can\n     * be customized via the `generatePayload` option the instance is created\n     * with):\n     *\n     * ```\n     * {\n     *   type: 'CACHE_UPDATED',\n     *   meta: 'workbox-broadcast-update',\n     *   payload: {\n     *     cacheName: 'the-cache-name',\n     *     updatedURL: 'https://example.com/'\n     *   }\n     * }\n     * ```\n     *\n     * @param {Object} options\n     * @param {Response} [options.oldResponse] Cached response to compare.\n     * @param {Response} options.newResponse Possibly updated response to compare.\n     * @param {Request} options.request The request.\n     * @param {string} options.cacheName Name of the cache the responses belong\n     *     to. This is included in the broadcast message.\n     * @param {Event} options.event event The event that triggered\n     *     this possible cache update.\n     * @return {Promise} Resolves once the update is sent.\n     */\n    async notifyIfUpdated(options) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(options.cacheName, 'string', {\n                moduleName: 'workbox-broadcast-update',\n                className: 'BroadcastCacheUpdate',\n                funcName: 'notifyIfUpdated',\n                paramName: 'cacheName',\n            });\n            assert.isInstance(options.newResponse, Response, {\n                moduleName: 'workbox-broadcast-update',\n                className: 'BroadcastCacheUpdate',\n                funcName: 'notifyIfUpdated',\n                paramName: 'newResponse',\n            });\n            assert.isInstance(options.request, Request, {\n                moduleName: 'workbox-broadcast-update',\n                className: 'BroadcastCacheUpdate',\n                funcName: 'notifyIfUpdated',\n                paramName: 'request',\n            });\n        }\n        // Without two responses there is nothing to compare.\n        if (!options.oldResponse) {\n            return;\n        }\n        if (!responsesAreSame(options.oldResponse, options.newResponse, this._headersToCheck)) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.log(`Newer response found (and cached) for:`, options.request.url);\n            }\n            const messageData = {\n                type: CACHE_UPDATED_MESSAGE_TYPE,\n                meta: CACHE_UPDATED_MESSAGE_META,\n                payload: this._generatePayload(options),\n            };\n            // For navigation requests, wait until the new window client exists\n            // before sending the message\n            if (options.request.mode === 'navigate') {\n                let resultingClientId;\n                if (options.event instanceof FetchEvent) {\n                    resultingClientId = options.event.resultingClientId;\n                }\n                const resultingWin = await resultingClientExists(resultingClientId);\n                // Safari does not currently implement postMessage buffering and\n                // there's no good way to feature detect that, so to increase the\n                // chances of the message being delivered in Safari, we add a timeout.\n                // We also do this if `resultingClientExists()` didn't return a client,\n                // which means it timed out, so it's worth waiting a bit longer.\n                if (!resultingWin || isSafari) {\n                    // 3500 is chosen because (according to CrUX data) 80% of mobile\n                    // websites hit the DOMContentLoaded event in less than 3.5 seconds.\n                    // And presumably sites implementing service worker are on the\n                    // higher end of the performance spectrum.\n                    await timeout(3500);\n                }\n            }\n            if (this._notifyAllClients) {\n                const windows = await self.clients.matchAll({ type: 'window' });\n                for (const win of windows) {\n                    win.postMessage(messageData);\n                }\n            }\n            else {\n                // See https://github.com/GoogleChrome/workbox/issues/2895\n                if (options.event instanceof FetchEvent) {\n                    const client = await self.clients.get(options.event.clientId);\n                    client === null || client === void 0 ? void 0 : client.postMessage(messageData);\n                }\n            }\n        }\n    }\n}\nexport { BroadcastCacheUpdate };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { dontWaitFor } from 'workbox-core/_private/dontWaitFor.js';\nimport { BroadcastCacheUpdate, } from './BroadcastCacheUpdate.js';\nimport './_version.js';\n/**\n * This plugin will automatically broadcast a message whenever a cached response\n * is updated.\n *\n * @memberof workbox-broadcast-update\n */\nclass BroadcastUpdatePlugin {\n    /**\n     * Construct a {@link workbox-broadcast-update.BroadcastUpdate} instance with\n     * the passed options and calls its `notifyIfUpdated` method whenever the\n     * plugin's `cacheDidUpdate` callback is invoked.\n     *\n     * @param {Object} [options]\n     * @param {Array<string>} [options.headersToCheck=['content-length', 'etag', 'last-modified']]\n     *     A list of headers that will be used to determine whether the responses\n     *     differ.\n     * @param {string} [options.generatePayload] A function whose return value\n     *     will be used as the `payload` field in any cache update messages sent\n     *     to the window clients.\n     */\n    constructor(options) {\n        /**\n         * A \"lifecycle\" callback that will be triggered automatically by the\n         * `workbox-sw` and `workbox-runtime-caching` handlers when an entry is\n         * added to a cache.\n         *\n         * @private\n         * @param {Object} options The input object to this function.\n         * @param {string} options.cacheName Name of the cache being updated.\n         * @param {Response} [options.oldResponse] The previous cached value, if any.\n         * @param {Response} options.newResponse The new value in the cache.\n         * @param {Request} options.request The request that triggered the update.\n         * @param {Request} options.event The event that triggered the update.\n         */\n        this.cacheDidUpdate = async (options) => {\n            dontWaitFor(this._broadcastUpdate.notifyIfUpdated(options));\n        };\n        this._broadcastUpdate = new BroadcastCacheUpdate(options);\n    }\n}\nexport { BroadcastUpdatePlugin };\n"], "names": ["self", "_", "e", "responsesAreSame", "firstResponse", "secondResponse", "headersToCheck", "Response", "WorkboxError", "atLeastOneHeaderAvailable", "some", "header", "headers", "has", "logger", "warn", "debug", "every", "headerStateComparison", "headerValueComparison", "get", "CACHE_UPDATED_MESSAGE_TYPE", "CACHE_UPDATED_MESSAGE_META", "NOTIFY_ALL_CLIENTS", "DEFAULT_HEADERS_TO_CHECK", "<PERSON><PERSON><PERSON><PERSON>", "test", "navigator", "userAgent", "defaultPayloadGenerator", "data", "cacheName", "updatedURL", "request", "url", "BroadcastCacheUpdate", "constructor", "generatePayload", "notifyAllClients", "_headersToCheck", "_generatePayload", "_notifyAllClients", "notifyIfUpdated", "options", "assert", "isType", "moduleName", "className", "funcName", "paramName", "isInstance", "newResponse", "Request", "oldResponse", "log", "messageData", "type", "meta", "payload", "mode", "resultingClientId", "event", "FetchEvent", "resultingWin", "resultingClientExists", "timeout", "windows", "clients", "matchAll", "win", "postMessage", "client", "clientId", "BroadcastUpdatePlugin", "cacheDidUpdate", "dontWait<PERSON>or", "_broadcastUpdate"], "mappings": ";;;;IAEA,IAAI;IACAA,EAAAA,IAAI,CAAC,gCAAD,CAAJ,IAA0CC,CAAC,EAA3C;IACH,CAFD,CAGA,OAAOC,CAAP,EAAU;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;UACMC,gBAAgB,GAAG,CAACC,aAAD,EAAgBC,cAAhB,EAAgCC,cAAhC,KAAmD;IACxE,EAA2C;IACvC,QAAI,EAAEF,aAAa,YAAYG,QAAzB,IAAqCF,cAAc,YAAYE,QAAjE,CAAJ,EAAgF;IAC5E,YAAM,IAAIC,4BAAJ,CAAiB,iCAAjB,CAAN;IACH;IACJ;;IACD,QAAMC,yBAAyB,GAAGH,cAAc,CAACI,IAAf,CAAqBC,MAAD,IAAY;IAC9D,WAAQP,aAAa,CAACQ,OAAd,CAAsBC,GAAtB,CAA0BF,MAA1B,KAAqCN,cAAc,CAACO,OAAf,CAAuBC,GAAvB,CAA2BF,MAA3B,CAA7C;IACH,GAFiC,CAAlC;;IAGA,MAAI,CAACF,yBAAL,EAAgC;IAC5B,IAA2C;IACvCK,MAAAA,gBAAM,CAACC,IAAP,CAAa,0DAAD,GACP,gEADL;IAEAD,MAAAA,gBAAM,CAACE,KAAP,CAAc,uCAAd,EAAsDZ,aAAtD,EAAqEC,cAArE,EAAqFC,cAArF;IACH,KAL2B;IAO5B;;;IACA,WAAO,IAAP;IACH;;IACD,SAAOA,cAAc,CAACW,KAAf,CAAsBN,MAAD,IAAY;IACpC,UAAMO,qBAAqB,GAAGd,aAAa,CAACQ,OAAd,CAAsBC,GAAtB,CAA0BF,MAA1B,MAAsCN,cAAc,CAACO,OAAf,CAAuBC,GAAvB,CAA2BF,MAA3B,CAApE;IACA,UAAMQ,qBAAqB,GAAGf,aAAa,CAACQ,OAAd,CAAsBQ,GAAtB,CAA0BT,MAA1B,MAAsCN,cAAc,CAACO,OAAf,CAAuBQ,GAAvB,CAA2BT,MAA3B,CAApE;IACA,WAAOO,qBAAqB,IAAIC,qBAAhC;IACH,GAJM,CAAP;IAKH;;IC7CD;IACA;AACA;IACA;IACA;IACA;IACA;IAEO,MAAME,0BAA0B,GAAG,eAAnC;IACA,MAAMC,0BAA0B,GAAG,0BAAnC;IACA,MAAMC,kBAAkB,GAAG,IAA3B;IACA,MAAMC,wBAAwB,GAAG,CACpC,gBADoC,EAEpC,MAFoC,EAGpC,eAHoC,CAAjC;;ICXP;IACA;AACA;IACA;IACA;IACA;IACA;IASA;IACA;;IACA,MAAMC,QAAQ,GAAG,iCAAiCC,IAAjC,CAAsCC,SAAS,CAACC,SAAhD,CAAjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASC,uBAAT,CAAiCC,IAAjC,EAAuC;IACnC,SAAO;IACHC,IAAAA,SAAS,EAAED,IAAI,CAACC,SADb;IAEHC,IAAAA,UAAU,EAAEF,IAAI,CAACG,OAAL,CAAaC;IAFtB,GAAP;IAIH;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,MAAMC,oBAAN,CAA2B;IACvB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIC,EAAAA,WAAW,CAAC;IAAEC,IAAAA,eAAF;IAAmB/B,IAAAA,cAAnB;IAAmCgC,IAAAA;IAAnC,MAAyD,EAA1D,EAA8D;IACrE,SAAKC,eAAL,GAAuBjC,cAAc,IAAIkB,wBAAzC;IACA,SAAKgB,gBAAL,GAAwBH,eAAe,IAAIR,uBAA3C;IACA,SAAKY,iBAAL,GAAyBH,gBAAgB,KAAK,IAArB,IAA6BA,gBAAgB,KAAK,KAAK,CAAvD,GAA2DA,gBAA3D,GAA8Ef,kBAAvG;IACH;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACI,QAAMmB,eAAN,CAAsBC,OAAtB,EAA+B;IAC3B,IAA2C;IACvCC,MAAAA,gBAAM,CAACC,MAAP,CAAcF,OAAO,CAACZ,SAAtB,EAAiC,QAAjC,EAA2C;IACvCe,QAAAA,UAAU,EAAE,0BAD2B;IAEvCC,QAAAA,SAAS,EAAE,sBAF4B;IAGvCC,QAAAA,QAAQ,EAAE,iBAH6B;IAIvCC,QAAAA,SAAS,EAAE;IAJ4B,OAA3C;IAMAL,MAAAA,gBAAM,CAACM,UAAP,CAAkBP,OAAO,CAACQ,WAA1B,EAAuC5C,QAAvC,EAAiD;IAC7CuC,QAAAA,UAAU,EAAE,0BADiC;IAE7CC,QAAAA,SAAS,EAAE,sBAFkC;IAG7CC,QAAAA,QAAQ,EAAE,iBAHmC;IAI7CC,QAAAA,SAAS,EAAE;IAJkC,OAAjD;IAMAL,MAAAA,gBAAM,CAACM,UAAP,CAAkBP,OAAO,CAACV,OAA1B,EAAmCmB,OAAnC,EAA4C;IACxCN,QAAAA,UAAU,EAAE,0BAD4B;IAExCC,QAAAA,SAAS,EAAE,sBAF6B;IAGxCC,QAAAA,QAAQ,EAAE,iBAH8B;IAIxCC,QAAAA,SAAS,EAAE;IAJ6B,OAA5C;IAMH,KApB0B;;;IAsB3B,QAAI,CAACN,OAAO,CAACU,WAAb,EAA0B;IACtB;IACH;;IACD,QAAI,CAAClD,gBAAgB,CAACwC,OAAO,CAACU,WAAT,EAAsBV,OAAO,CAACQ,WAA9B,EAA2C,KAAKZ,eAAhD,CAArB,EAAuF;IACnF,MAA2C;IACvCzB,QAAAA,gBAAM,CAACwC,GAAP,CAAY,wCAAZ,EAAqDX,OAAO,CAACV,OAAR,CAAgBC,GAArE;IACH;;IACD,YAAMqB,WAAW,GAAG;IAChBC,QAAAA,IAAI,EAAEnC,0BADU;IAEhBoC,QAAAA,IAAI,EAAEnC,0BAFU;IAGhBoC,QAAAA,OAAO,EAAE,KAAKlB,gBAAL,CAAsBG,OAAtB;IAHO,OAApB,CAJmF;IAUnF;;IACA,UAAIA,OAAO,CAACV,OAAR,CAAgB0B,IAAhB,KAAyB,UAA7B,EAAyC;IACrC,YAAIC,iBAAJ;;IACA,YAAIjB,OAAO,CAACkB,KAAR,YAAyBC,UAA7B,EAAyC;IACrCF,UAAAA,iBAAiB,GAAGjB,OAAO,CAACkB,KAAR,CAAcD,iBAAlC;IACH;;IACD,cAAMG,YAAY,GAAG,MAAMC,8CAAqB,CAACJ,iBAAD,CAAhD,CALqC;IAOrC;IACA;IACA;IACA;;IACA,YAAI,CAACG,YAAD,IAAiBtC,QAArB,EAA+B;IAC3B;IACA;IACA;IACA;IACA,gBAAMwC,kBAAO,CAAC,IAAD,CAAb;IACH;IACJ;;IACD,UAAI,KAAKxB,iBAAT,EAA4B;IACxB,cAAMyB,OAAO,GAAG,MAAMlE,IAAI,CAACmE,OAAL,CAAaC,QAAb,CAAsB;IAAEZ,UAAAA,IAAI,EAAE;IAAR,SAAtB,CAAtB;;IACA,aAAK,MAAMa,GAAX,IAAkBH,OAAlB,EAA2B;IACvBG,UAAAA,GAAG,CAACC,WAAJ,CAAgBf,WAAhB;IACH;IACJ,OALD,MAMK;IACD;IACA,YAAIZ,OAAO,CAACkB,KAAR,YAAyBC,UAA7B,EAAyC;IACrC,gBAAMS,MAAM,GAAG,MAAMvE,IAAI,CAACmE,OAAL,CAAa/C,GAAb,CAAiBuB,OAAO,CAACkB,KAAR,CAAcW,QAA/B,CAArB;IACAD,UAAAA,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,KAAK,CAAnC,GAAuC,KAAK,CAA5C,GAAgDA,MAAM,CAACD,WAAP,CAAmBf,WAAnB,CAAhD;IACH;IACJ;IACJ;IACJ;;IAzHsB;;ICxC3B;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;;IACA,MAAMkB,qBAAN,CAA4B;IACxB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIrC,EAAAA,WAAW,CAACO,OAAD,EAAU;IACjB;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACQ,SAAK+B,cAAL,GAAsB,MAAO/B,OAAP,IAAmB;IACrCgC,MAAAA,0BAAW,CAAC,KAAKC,gBAAL,CAAsBlC,eAAtB,CAAsCC,OAAtC,CAAD,CAAX;IACH,KAFD;;IAGA,SAAKiC,gBAAL,GAAwB,IAAIzC,oBAAJ,CAAyBQ,OAAzB,CAAxB;IACH;;IAhCuB;;;;;;;;;;;;"}
{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst UsersRound = createLucideIcon(\"UsersRound\", [[\"path\", {\n  d: \"M18 21a8 8 0 0 0-16 0\",\n  key: \"3ypg7q\"\n}], [\"circle\", {\n  cx: \"10\",\n  cy: \"8\",\n  r: \"5\",\n  key: \"o932ke\"\n}], [\"path\", {\n  d: \"M22 20c0-3.37-2-6.5-4-8a5 5 0 0 0-.45-8.3\",\n  key: \"10s06x\"\n}]]);\nexport { UsersRound as default };\n//# sourceMappingURL=users-round.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
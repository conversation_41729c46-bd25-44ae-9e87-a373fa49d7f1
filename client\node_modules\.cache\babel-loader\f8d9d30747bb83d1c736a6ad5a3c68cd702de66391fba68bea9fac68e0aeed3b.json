{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst CornerUpRight = createLucideIcon(\"CornerUpRight\", [[\"polyline\", {\n  points: \"15 14 20 9 15 4\",\n  key: \"1tbx3s\"\n}], [\"path\", {\n  d: \"M4 20v-7a4 4 0 0 1 4-4h12\",\n  key: \"1lu4f8\"\n}]]);\nexport { CornerUpRight as default };\n//# sourceMappingURL=corner-up-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
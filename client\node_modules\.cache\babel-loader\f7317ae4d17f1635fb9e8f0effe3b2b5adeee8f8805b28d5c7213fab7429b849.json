{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Banknote = createLucideIcon(\"Banknote\", [[\"rect\", {\n  width: \"20\",\n  height: \"12\",\n  x: \"2\",\n  y: \"6\",\n  rx: \"2\",\n  key: \"9lu3g6\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"2\",\n  key: \"1c9p78\"\n}], [\"path\", {\n  d: \"M6 12h.01M18 12h.01\",\n  key: \"113zkx\"\n}]]);\nexport { Banknote as default };\n//# sourceMappingURL=banknote.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst UserMinus = createLucideIcon(\"UserMinus\", [[\"path\", {\n  d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\",\n  key: \"1yyitq\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"7\",\n  r: \"4\",\n  key: \"nufk8\"\n}], [\"line\", {\n  x1: \"22\",\n  x2: \"16\",\n  y1: \"11\",\n  y2: \"11\",\n  key: \"1shjgl\"\n}]]);\nexport { UserMinus as default };\n//# sourceMappingURL=user-minus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
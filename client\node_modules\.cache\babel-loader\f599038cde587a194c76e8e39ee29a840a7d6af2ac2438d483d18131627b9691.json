{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Laptop = createLucideIcon(\"Laptop\", [[\"path\", {\n  d: \"M20 16V7a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v9m16 0H4m16 0 1.28 2.55a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45L4 16\",\n  key: \"tarvll\"\n}]]);\nexport { Laptop as default };\n//# sourceMappingURL=laptop.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
const express = require('express');
const cors = require('cors');
const axios = require('axios');
const multer = require('multer');
const path = require('path');
const FormData = require('form-data');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../client/build')));

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

// Together AI API configuration
const TOGETHER_API_KEY = process.env.TOGETHER_API_KEY;
const TOGETHER_API_BASE_URL = process.env.TOGETHER_API_BASE_URL;

const togetherAPI = axios.create({
  baseURL: TOGETHER_API_BASE_URL,
  headers: {
    'Authorization': `<PERSON><PERSON> ${TOGETHER_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

// API Routes

// Create a fine-tuning job
app.post('/api/fine-tunes', async (req, res) => {
  try {
    const response = await togetherAPI.post('/fine-tunes', req.body);
    res.json(response.data);
  } catch (error) {
    console.error('Error creating fine-tune job:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: error.response?.data || { message: 'Internal server error' }
    });
  }
});

// List all fine-tuning jobs
app.get('/api/fine-tunes', async (req, res) => {
  try {
    const response = await togetherAPI.get('/fine-tunes');
    res.json(response.data);
  } catch (error) {
    console.error('Error fetching fine-tune jobs:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: error.response?.data || { message: 'Internal server error' }
    });
  }
});

// Get a specific fine-tuning job
app.get('/api/fine-tunes/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const response = await togetherAPI.get(`/fine-tunes/${id}`);
    res.json(response.data);
  } catch (error) {
    console.error('Error fetching fine-tune job:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: error.response?.data || { message: 'Internal server error' }
    });
  }
});

// Get events for a fine-tuning job
app.get('/api/fine-tunes/:id/events', async (req, res) => {
  try {
    const { id } = req.params;
    const response = await togetherAPI.get(`/fine-tunes/${id}/events`);
    res.json(response.data);
  } catch (error) {
    console.error('Error fetching fine-tune events:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: error.response?.data || { message: 'Internal server error' }
    });
  }
});

// Get checkpoints for a fine-tuning job
app.get('/api/fine-tunes/:id/checkpoints', async (req, res) => {
  try {
    const { id } = req.params;
    const response = await togetherAPI.get(`/fine-tunes/${id}/checkpoints`);
    res.json(response.data);
  } catch (error) {
    console.error('Error fetching fine-tune checkpoints:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: error.response?.data || { message: 'Internal server error' }
    });
  }
});

// Cancel a fine-tuning job
app.post('/api/fine-tunes/:id/cancel', async (req, res) => {
  try {
    const { id } = req.params;
    const response = await togetherAPI.post(`/fine-tunes/${id}/cancel`);
    res.json(response.data);
  } catch (error) {
    console.error('Error cancelling fine-tune job:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: error.response?.data || { message: 'Internal server error' }
    });
  }
});

// Download a fine-tuned model
app.get('/api/finetune/download', async (req, res) => {
  try {
    const response = await togetherAPI.get('/finetune/download', {
      params: req.query
    });
    res.json(response.data);
  } catch (error) {
    console.error('Error downloading model:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: error.response?.data || { message: 'Internal server error' }
    });
  }
});

// File management endpoints

// List all files
app.get('/api/files', async (req, res) => {
  try {
    const response = await togetherAPI.get('/files');
    res.json(response.data);
  } catch (error) {
    console.error('Error fetching files:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: error.response?.data || { message: 'Internal server error' }
    });
  }
});

// Get specific file details
app.get('/api/files/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const response = await togetherAPI.get(`/files/${id}`);
    res.json(response.data);
  } catch (error) {
    console.error('Error fetching file:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: error.response?.data || { message: 'Internal server error' }
    });
  }
});

// Get file content
app.get('/api/files/:id/content', async (req, res) => {
  try {
    const { id } = req.params;
    const response = await togetherAPI.get(`/files/${id}/content`);
    res.json(response.data);
  } catch (error) {
    console.error('Error fetching file content:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: error.response?.data || { message: 'Internal server error' }
    });
  }
});

// Delete file
app.delete('/api/files/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const response = await togetherAPI.delete(`/files/${id}`);
    res.json(response.data);
  } catch (error) {
    console.error('Error deleting file:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: error.response?.data || { message: 'Internal server error' }
    });
  }
});

// Upload file to Together AI
app.post('/api/files/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const { purpose = 'fine-tune', file_type } = req.body;
    
    // Create form data for Together AI upload
    const FormData = require('form-data');
    const formData = new FormData();
    
    formData.append('file', req.file.buffer, {
      filename: req.file.originalname,
      contentType: req.file.mimetype
    });
    formData.append('purpose', purpose);
    formData.append('file_name', req.file.originalname);
    
    if (file_type) {
      formData.append('file_type', file_type);
    }

    // Upload to Together AI
    const response = await axios.post(`${TOGETHER_API_BASE_URL}/files/upload`, formData, {
      headers: {
        'Authorization': `Bearer ${TOGETHER_API_KEY}`,
        ...formData.getHeaders()
      }
    });
    
    res.json(response.data);
  } catch (error) {
    console.error('Error uploading file:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: error.response?.data || { message: 'File upload failed' }
    });
  }
});

// List available hardware configurations
app.get('/api/hardware', async (req, res) => {
  try {
    const response = await togetherAPI.get('/hardware', {
      params: req.query
    });
    if (response?.data) {
      console.log('Returning successful API response to frontend');
      res.json(response.data);
    } else {
      console.log('API response was empty, using fallback');
      // Continue to fallback logic below
    }
  } catch (error) {
    console.error('Error fetching hardware:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: error.response?.data || { message: 'Internal server error' }
    });
  }
});

// Get available models for fine-tuning
app.get('/api/models', async (req, res) => {
  try {
    console.log('Attempting to fetch models from Together AI API at /models...');
    const response = await togetherAPI.get('/models');

    console.log('Successfully fetched models from Together AI API:', {
      status: response.status,
      dataType: typeof response.data,
      isArray: Array.isArray(response.data),
      dataLength: Array.isArray(response.data) ? response.data.length : 'N/A'
    });

    // The Together AI API returns an array of models directly, not wrapped in a data property
    if (Array.isArray(response.data)) {
      // Filter for fine-tunable models (all Qwen and Llama models, plus models with finetune pricing or specific known models)
      const finetuneableModels = response.data.filter(model => {
        // Include models that have finetune pricing
        const hasFinetunePricing = model.pricing && model.pricing.finetune > 0;

        // Include all Qwen and Llama models (case-insensitive)
        const modelIdLower = model.id.toLowerCase();
        const isQwenOrLlama = modelIdLower.includes('qwen') || modelIdLower.includes('llama');

        // Include other known fine-tunable models
        const isKnownFinetunable = [
          'mistralai/Mistral-7B-v0.1',
          'mistralai/Mixtral-8x7B-v0.1',
          'NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO',
          'togethercomputer/RedPajama-INCITE-Chat-3B-v1',
          'togethercomputer/RedPajama-INCITE-7B-Chat',
          'WizardLM/WizardLM-13B-V1.2',
          'teknium/OpenHermes-2.5-Mistral-7B'
        ].includes(model.id);

        // Include chat and language models that are Qwen/Llama, have finetune pricing, or are known fine-tunable
        return (model.type === 'chat' || model.type === 'language') && (isQwenOrLlama || hasFinetunePricing || isKnownFinetunable);
      });

      console.log(`Filtered ${finetuneableModels.length} fine-tunable models from ${response.data.length} total models`);

      // Return in the expected format with data wrapper
      return res.json({
        data: finetuneableModels
      });
    }

    // If response is not an array, fall back to known models
    console.log('API response is not an array, using fallback model list');

    const knownModels = {
        data: [
          // Llama models
          {
            id: 'meta-llama/Meta-Llama-3.1-8B-Instruct-Reference',
            type: 'chat',
            created: Date.now(),
            owned_by: 'meta-llama'
          },
          {
            id: 'meta-llama/Meta-Llama-3.1-70B-Instruct-Reference',
            type: 'chat',
            created: Date.now(),
            owned_by: 'meta-llama'
          },
          {
            id: 'meta-llama/Llama-2-7b-hf',
            type: 'language',
            created: Date.now(),
            owned_by: 'meta-llama'
          },
          {
            id: 'meta-llama/Llama-2-13b-hf',
            type: 'language',
            created: Date.now(),
            owned_by: 'meta-llama'
          },
          {
            id: 'meta-llama/Llama-2-70b-hf',
            type: 'language',
            created: Date.now(),
            owned_by: 'meta-llama'
          },
          {
            id: 'meta-llama/Llama-3-8b-hf',
            type: 'language',
            created: Date.now(),
            owned_by: 'meta-llama'
          },
          {
            id: 'meta-llama/Llama-3-70b-hf',
            type: 'language',
            created: Date.now(),
            owned_by: 'meta-llama'
          },
          // Qwen models
          {
            id: 'Qwen/Qwen2-7B-Instruct',
            type: 'chat',
            created: Date.now(),
            owned_by: 'Qwen'
          },
          {
            id: 'Qwen/Qwen2-72B-Instruct',
            type: 'chat',
            created: Date.now(),
            owned_by: 'Qwen'
          },
          {
            id: 'Qwen/Qwen1.5-7B-Chat',
            type: 'chat',
            created: Date.now(),
            owned_by: 'Qwen'
          },
          {
            id: 'Qwen/Qwen1.5-14B-Chat',
            type: 'chat',
            created: Date.now(),
            owned_by: 'Qwen'
          },
          // Other models
          {
            id: 'mistralai/Mistral-7B-v0.1',
            type: 'language',
            created: Date.now(),
            owned_by: 'mistralai'
          },
          {
            id: 'mistralai/Mixtral-8x7B-v0.1',
            type: 'language',
            created: Date.now(),
            owned_by: 'mistralai'
          }
        ]
      };
      
    console.log('Using fallback model list');
    return res.json(knownModels);
  } catch (error) {
    console.error('Error fetching models:', error.response?.data || error.message);
    
    // Provide fallback models even if everything fails
    const fallbackModels = {
      data: [
        {
          id: 'meta-llama/Meta-Llama-3.1-8B-Instruct-Reference',
          type: 'chat',
          created: Date.now(),
          owned_by: 'meta-llama'
        },
        {
          id: 'meta-llama/Llama-2-7b-hf',
          type: 'language',
          created: Date.now(),
          owned_by: 'meta-llama'
        },
        {
          id: 'mistralai/Mistral-7B-v0.1',
          type: 'language',
          created: Date.now(),
          owned_by: 'mistralai'
        }
      ]
    };
    
    console.log('All API attempts failed, using minimal fallback model list');
    res.json(fallbackModels);
  }
});

// Batch Jobs endpoints

// List all batch jobs
app.get('/api/batches', async (req, res) => {
  try {
    const response = await togetherAPI.get('/batches');
    res.json(response.data);
  } catch (error) {
    console.error('Error fetching batch jobs:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: error.response?.data || { message: 'Internal server error' }
    });
  }
});

// Create a new batch job
app.post('/api/batches', async (req, res) => {
  try {
    const response = await togetherAPI.post('/batches', req.body);
    res.status(201).json(response.data);
  } catch (error) {
    console.error('Error creating batch job:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: error.response?.data || { message: 'Internal server error' }
    });
  }
});

// Get a specific batch job
app.get('/api/batches/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const response = await togetherAPI.get(`/batches/${id}`);
    res.json(response.data);
  } catch (error) {
    console.error('Error fetching batch job:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: error.response?.data || { message: 'Internal server error' }
    });
  }
});

// Cancel a batch job
app.post('/api/batches/:id/cancel', async (req, res) => {
  try {
    const { id } = req.params;
    const response = await togetherAPI.post(`/batches/${id}/cancel`);
    res.json(response.data);
  } catch (error) {
    console.error('Error cancelling batch job:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: error.response?.data || { message: 'Internal server error' }
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Serve React app for any non-API routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../client/build/index.html'));
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({ error: { message: 'Internal server error' } });
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Together AI API configured with base URL: ${TOGETHER_API_BASE_URL}`);
});
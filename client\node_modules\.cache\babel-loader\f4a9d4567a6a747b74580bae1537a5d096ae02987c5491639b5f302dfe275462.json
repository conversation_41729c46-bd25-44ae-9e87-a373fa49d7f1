{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Sticker = createLucideIcon(\"Sticker\", [[\"path\", {\n  d: \"M15.5 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2V8.5L15.5 3Z\",\n  key: \"1wis1t\"\n}], [\"path\", {\n  d: \"M15 3v6h6\",\n  key: \"edgan2\"\n}], [\"path\", {\n  d: \"M10 16s.8 1 2 1c1.3 0 2-1 2-1\",\n  key: \"1vvgv3\"\n}], [\"path\", {\n  d: \"M8 13h0\",\n  key: \"jdup5h\"\n}], [\"path\", {\n  d: \"M16 13h0\",\n  key: \"l4i2ga\"\n}]]);\nexport { Sticker as default };\n//# sourceMappingURL=sticker.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
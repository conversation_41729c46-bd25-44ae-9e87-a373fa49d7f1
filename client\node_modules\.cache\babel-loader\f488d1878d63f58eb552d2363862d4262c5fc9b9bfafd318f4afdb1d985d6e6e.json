{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst BookCheck = createLucideIcon(\"BookCheck\", [[\"path\", {\n  d: \"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20\",\n  key: \"t4utmx\"\n}], [\"path\", {\n  d: \"m9 9.5 2 2 4-4\",\n  key: \"1dth82\"\n}]]);\nexport { BookCheck as default };\n//# sourceMappingURL=book-check.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Factory = createLucideIcon(\"Factory\", [[\"path\", {\n  d: \"M2 20a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8l-7 5V8l-7 5V4a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z\",\n  key: \"159hny\"\n}], [\"path\", {\n  d: \"M17 18h1\",\n  key: \"uldtlt\"\n}], [\"path\", {\n  d: \"M12 18h1\",\n  key: \"s9uhes\"\n}], [\"path\", {\n  d: \"M7 18h1\",\n  key: \"1neino\"\n}]]);\nexport { Factory as default };\n//# sourceMappingURL=factory.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"version": 3, "file": "hooks.js", "sourceRoot": "", "sources": ["../src/hooks.ts"], "names": [], "mappings": ";;;AAAA,2BAA8C;AAC9C,+BAA+C;AAE/C,qCAA4C;AAI5C,qDAA4C;AAI5C,uCAOmB;AAwBnB,MAAM,eAAe,GAAG,IAAI,OAAO,EAAE,CAAC;AAEtC,MAAM,gBAAgB,GAAG,CAAC,QAAkB,EAAE,EAAE;IAC9C,IAAI,KAAK,GAAG,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC1C,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;QAChC,KAAK,GAAG;YACN,SAAS,EAAE,IAAI,2BAAiB,CAAC,CAAC,UAAU,CAAC,CAAC;YAC9C,UAAU,EAAE,IAAI,2BAAiB,CAAC,CAAC,UAAU,CAAC,CAAC;SAChD,CAAC;QACF,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;KACtC;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AA2IgC,4CAAgB;AAzIlD,MAAM,aAAa,GAAG,CACpB,EAAE,YAAY,EAAE,gBAAgB,EAAqB,EACrD,CAAW,EACX,QAAkB,EAClB,EAAE;IACF,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAC1D,YAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC;IAGlD,IAAI,QAAQ,EAAE;QACZ,QAAQ,EAAE,CAAC;KACZ;AACH,CAAC,CAAC;AA6HO,sCAAa;AA3HtB,MAAM,QAAQ,GAAG,SAAS,IAAI,CAC5B,EACE,QAAQ,EACR,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,YAAY,EACZ,OAAO,EACM,EACf,WAA4C;IAE5C,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;IAEzD,MAAM,KAAK,GAAG,WAAW,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC;QAC1C,GAAG,EAAE,KAAK;QACV,MAAM,EAAE,IAAI;QACZ,YAAY,EAAE,IAAI;QAElB,GAAG,EAAE,IAAI;QACT,UAAU,EAAE,IAAI;KACa,CAAC,CAAC;IAEjC,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC;IACvF,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;IAE5C,YAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;IAE9C,MAAM,cAAc,GAAqB,EAAE,CAAC;IAC5C,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,MAAM,CAC/C,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,qBAAW,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,cAAc,CAAC,EAClE,EAAsB,CACvB,CAAC;IAGF,KAAK,GAAI,KAAK,CAAC,MAAyC,CAAC,MAAM,CAC7D,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,sBAAY,CAAC,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC,EACxD,KAAK,CACN,CAAC;IAGF,KAAK,GAAG,KAAK,CAAC,MAAM,CAClB,CAAC,EAAE,IAAI,EAAE,IAAI,EAAkC,EAAE,EAAE;;QACjD,OAAA,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;YAC5B,OAAO,YAAY,CAAC,GAAG,CAAC,WAAI,CAAC,CAAA,MAAA,QAAQ,CAAC,OAAO,CAAC,MAAM,0CAAE,IAAI,KAAI,WAAW,EAAE,IAAI,CAAC,CAAC;gBAC/E,WAAW,CAAA;KAAA,CAChB,CAAC;IAIF,KAAK,CAAC,OAAO,CAAC,CAAC,IAAoB,EAAE,EAAE;QACrC,OAAO,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;IAGH,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;QACpD,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAoB,EAAE,EAAE;QACzC,MAAM,aAAa,GAAG,CAAC,IAAY,EAAU,EAAE;YAC7C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBACvB,OAAO,GAAG,IAAI,GAAG,CAAC;aACnB;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;QAEF,MAAM,OAAO,GAAG;YAEd,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI;YAGhE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI;SACrE,CAAC;QAGF,OAAO,CAAC,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;QAEtF,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,KAAK,GAAG,wBAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAEvC,IAAI,QAAQ,GAAG,0BAAgB,CAAC,WAAW,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAC7D,MAAM,UAAU,GAAG,SAAS,KAAK,CAAC,CAAC;IAEnC,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAEhE,IAAI,UAAU,EAAE;QACd,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE1C,WAA0C,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,2BAAS,CAAC,MAAM,CAAC,CAAC,CAAC;QAE9F,IAAI,OAAO,CAAC,eAAe,EAAE;YAC3B,cAAS,CAAC,cAAO,CAAC,gBAAgB,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAC1D,kBAAa,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;SACzC;KACF;IAED,gBAAgB,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACtD,CAAC,CAAC;AAuBsB,4BAAQ;AAjBhC,MAAM,sBAAsB,GAAG,CAC7B,EAAE,YAAY,EAAsC,EACpD,aAA2C,EAC3C,MAAoB,EACpB,EAAE;IACF,MAAM,EAAE,QAAQ,EAAE,GAAG,aAAa,CAAC;IAGnC,aAAa,CAAC,QAAQ,GAAG,CAAC,IAAY,EAAE,OAAe,EAAE,SAAc,EAAE,EAAE;QACzE,IAAI,MAAM,CAAC,WAAW,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;YAC7C,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,WAAI,CAAC,cAAO,CAAC,IAAI,CAAC,EAAE,eAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;SAC5F;QAED,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;IACzD,CAAC,CAAC;AACJ,CAAC,CAAC;AAEkD,wDAAsB"}
{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Together\\\\client\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport Layout from './components/Layout';\nimport Dashboard from './pages/Dashboard';\nimport CreateJob from './pages/CreateJob';\nimport JobDetails from './pages/JobDetails';\nimport JobList from './pages/JobList';\nimport Files from './pages/Files';\nimport Hardware from './pages/Hardware';\nimport Batches from './pages/Batches';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(Layout, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/dashboard\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/jobs\",\n          element: /*#__PURE__*/_jsxDEV(JobList, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/jobs/create\",\n          element: /*#__PURE__*/_jsxDEV(CreateJob, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/jobs/:id\",\n          element: /*#__PURE__*/_jsxDEV(JobDetails, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/batches\",\n          element: /*#__PURE__*/_jsxDEV(Batches, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/files\",\n          element: /*#__PURE__*/_jsxDEV(Files, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/hardware\",\n          element: /*#__PURE__*/_jsxDEV(Hardware, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Layout", "Dashboard", "<PERSON><PERSON><PERSON><PERSON>", "JobDetails", "JobList", "Files", "Hardware", "Batches", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Together/client/src/App.js"], "sourcesContent": ["import React from 'react';\r\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\r\nimport Layout from './components/Layout';\r\nimport Dashboard from './pages/Dashboard';\r\nimport CreateJob from './pages/CreateJob';\r\nimport JobDetails from './pages/JobDetails';\r\nimport JobList from './pages/JobList';\r\nimport Files from './pages/Files';\r\nimport Hardware from './pages/Hardware';\r\nimport Batches from './pages/Batches';\r\n\r\nfunction App() {\r\n  return (\r\n    <Router>\r\n      <Layout>\r\n        <Routes>\r\n          <Route path=\"/\" element={<Navigate to=\"/dashboard\" replace />} />\r\n          <Route path=\"/dashboard\" element={<Dashboard />} />\r\n          <Route path=\"/jobs\" element={<JobList />} />\r\n          <Route path=\"/jobs/create\" element={<CreateJob />} />\r\n          <Route path=\"/jobs/:id\" element={<JobDetails />} />\r\n          <Route path=\"/batches\" element={<Batches />} />\r\n          <Route path=\"/files\" element={<Files />} />\r\n          <Route path=\"/hardware\" element={<Hardware />} />\r\n        </Routes>\r\n      </Layout>\r\n    </Router>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,OAAO,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACb,MAAM;IAAAe,QAAA,eACLF,OAAA,CAACT,MAAM;MAAAW,QAAA,eACLF,OAAA,CAACZ,MAAM;QAAAc,QAAA,gBACLF,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEJ,OAAA,CAACV,QAAQ;YAACe,EAAE,EAAC,YAAY;YAACC,OAAO;UAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjEV,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEJ,OAAA,CAACR,SAAS;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnDV,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,OAAO;UAACC,OAAO,eAAEJ,OAAA,CAACL,OAAO;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5CV,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,cAAc;UAACC,OAAO,eAAEJ,OAAA,CAACP,SAAS;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDV,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEJ,OAAA,CAACN,UAAU;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnDV,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,UAAU;UAACC,OAAO,eAAEJ,OAAA,CAACF,OAAO;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/CV,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEJ,OAAA,CAACJ,KAAK;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CV,OAAA,CAACX,KAAK;UAACc,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEJ,OAAA,CAACH,QAAQ;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb;AAACC,EAAA,GAjBQV,GAAG;AAmBZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst WrapText = createLucideIcon(\"WrapText\", [[\"line\", {\n  x1: \"3\",\n  x2: \"21\",\n  y1: \"6\",\n  y2: \"6\",\n  key: \"4m8b97\"\n}], [\"path\", {\n  d: \"M3 12h15a3 3 0 1 1 0 6h-4\",\n  key: \"1cl7v7\"\n}], [\"polyline\", {\n  points: \"16 16 14 18 16 20\",\n  key: \"1jznyi\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"10\",\n  y1: \"18\",\n  y2: \"18\",\n  key: \"1h33wv\"\n}]]);\nexport { WrapText as default };\n//# sourceMappingURL=wrap-text.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.NODE_ENV === 'production' ? '' : 'http://localhost:3001';\nconst api = axios.create({\n  baseURL: `${API_BASE_URL}/api`,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor for logging\napi.interceptors.request.use(config => {\n  var _config$method;\n  console.log(`Making ${(_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase()} request to ${config.url}`);\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor for error handling\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  var _error$response;\n  console.error('API Error:', ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error.message);\n  return Promise.reject(error);\n});\nexport const finetuneAPI = {\n  // Create a new fine-tuning job\n  createJob: jobData => api.post('/fine-tunes', jobData),\n  // Get all fine-tuning jobs\n  getJobs: () => api.get('/fine-tunes'),\n  // Get a specific fine-tuning job\n  getJob: id => api.get(`/fine-tunes/${id}`),\n  // Get events for a fine-tuning job\n  getJobEvents: id => api.get(`/fine-tunes/${id}/events`),\n  // Get checkpoints for a fine-tuning job\n  getJobCheckpoints: id => api.get(`/fine-tunes/${id}/checkpoints`),\n  // Cancel a fine-tuning job\n  cancelJob: id => api.post(`/fine-tunes/${id}/cancel`),\n  // Download a fine-tuned model\n  downloadModel: params => api.get('/finetune/download', {\n    params\n  }),\n  // File management\n  // List all files\n  getFiles: () => api.get('/files'),\n  // Get specific file details\n  getFile: id => api.get(`/files/${id}`),\n  // Get file content\n  getFileContent: id => api.get(`/files/${id}/content`),\n  // Delete file\n  deleteFile: id => api.delete(`/files/${id}`),\n  // Upload a training file\n  uploadFile: (file, purpose = 'fine-tune', fileType = null) => {\n    const formData = new FormData();\n    formData.append('file', file);\n    formData.append('purpose', purpose);\n    if (fileType) {\n      formData.append('file_type', fileType);\n    }\n    return api.post('/files/upload', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n  },\n  // Get available hardware configurations\n  getHardware: (model = null) => api.get('/hardware', {\n    params: model ? {\n      model\n    } : {}\n  }),\n  // Get available models for fine-tuning\n  getModels: () => api.get('/models'),\n  // Batch Jobs API\n  // List all batch jobs\n  getBatches: () => api.get('/batches'),\n  // Create a new batch job\n  createBatch: batchData => api.post('/batches', batchData),\n  // Get a specific batch job\n  getBatch: id => api.get(`/batches/${id}`),\n  // Cancel a batch job\n  cancelBatch: id => api.post(`/batches/${id}/cancel`),\n  // Health check\n  healthCheck: () => api.get('/health')\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "NODE_ENV", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "_config$method", "console", "log", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "_error$response", "data", "message", "finetuneAPI", "createJob", "jobData", "post", "getJobs", "get", "get<PERSON>ob", "id", "getJobEvents", "getJobCheckpoints", "cancelJob", "downloadModel", "params", "getFiles", "getFile", "getFileContent", "deleteFile", "delete", "uploadFile", "file", "purpose", "fileType", "formData", "FormData", "append", "getHardware", "model", "getModels", "getBatches", "createBatch", "batchData", "getBatch", "cancelBatch", "healthCheck"], "sources": ["C:/Users/<USER>/Desktop/Together/client/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst API_BASE_URL = process.env.NODE_ENV === 'production'\r\n  ? ''\r\n  : 'http://localhost:3001';\r\n\r\nconst api = axios.create({\r\n  baseURL: `${API_BASE_URL}/api`,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// Request interceptor for logging\r\napi.interceptors.request.use(\r\n  (config) => {\r\n    console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`);\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor for error handling\r\napi.interceptors.response.use(\r\n  (response) => {\r\n    return response;\r\n  },\r\n  (error) => {\r\n    console.error('API Error:', error.response?.data || error.message);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport const finetuneAPI = {\r\n  // Create a new fine-tuning job\r\n  createJob: (jobData) => api.post('/fine-tunes', jobData),\r\n  \r\n  // Get all fine-tuning jobs\r\n  getJobs: () => api.get('/fine-tunes'),\r\n  \r\n  // Get a specific fine-tuning job\r\n  getJob: (id) => api.get(`/fine-tunes/${id}`),\r\n  \r\n  // Get events for a fine-tuning job\r\n  getJobEvents: (id) => api.get(`/fine-tunes/${id}/events`),\r\n  \r\n  // Get checkpoints for a fine-tuning job\r\n  getJobCheckpoints: (id) => api.get(`/fine-tunes/${id}/checkpoints`),\r\n  \r\n  // Cancel a fine-tuning job\r\n  cancelJob: (id) => api.post(`/fine-tunes/${id}/cancel`),\r\n  \r\n  // Download a fine-tuned model\r\n  downloadModel: (params) => api.get('/finetune/download', { params }),\r\n  \r\n  // File management\r\n  // List all files\r\n  getFiles: () => api.get('/files'),\r\n  \r\n  // Get specific file details\r\n  getFile: (id) => api.get(`/files/${id}`),\r\n  \r\n  // Get file content\r\n  getFileContent: (id) => api.get(`/files/${id}/content`),\r\n  \r\n  // Delete file\r\n  deleteFile: (id) => api.delete(`/files/${id}`),\r\n  \r\n  // Upload a training file\r\n  uploadFile: (file, purpose = 'fine-tune', fileType = null) => {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    formData.append('purpose', purpose);\r\n    if (fileType) {\r\n      formData.append('file_type', fileType);\r\n    }\r\n    return api.post('/files/upload', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n  },\r\n  \r\n  // Get available hardware configurations\r\n  getHardware: (model = null) => api.get('/hardware', { params: model ? { model } : {} }),\r\n  \r\n  // Get available models for fine-tuning\r\n  getModels: () => api.get('/models'),\r\n  \r\n  // Batch Jobs API\r\n  // List all batch jobs\r\n  getBatches: () => api.get('/batches'),\r\n  \r\n  // Create a new batch job\r\n  createBatch: (batchData) => api.post('/batches', batchData),\r\n  \r\n  // Get a specific batch job\r\n  getBatch: (id) => api.get(`/batches/${id}`),\r\n  \r\n  // Cancel a batch job\r\n  cancelBatch: (id) => api.post(`/batches/${id}/cancel`),\r\n  \r\n  // Health check\r\n  healthCheck: () => api.get('/health'),\r\n};\r\n\r\nexport default api;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GACtD,EAAE,GACF,uBAAuB;AAE3B,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAE,GAAGN,YAAY,MAAM;EAC9BO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EAAA,IAAAC,cAAA;EACVC,OAAO,CAACC,GAAG,CAAC,WAAAF,cAAA,GAAUD,MAAM,CAACI,MAAM,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,eAAeL,MAAM,CAACM,GAAG,EAAE,CAAC;EAC9E,OAAON,MAAM;AACf,CAAC,EACAO,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAd,GAAG,CAACI,YAAY,CAACa,QAAQ,CAACX,GAAG,CAC1BW,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACTT,OAAO,CAACK,KAAK,CAAC,YAAY,EAAE,EAAAI,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,IAAI,KAAIL,KAAK,CAACM,OAAO,CAAC;EAClE,OAAOL,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,OAAO,MAAMO,WAAW,GAAG;EACzB;EACAC,SAAS,EAAGC,OAAO,IAAKvB,GAAG,CAACwB,IAAI,CAAC,aAAa,EAAED,OAAO,CAAC;EAExD;EACAE,OAAO,EAAEA,CAAA,KAAMzB,GAAG,CAAC0B,GAAG,CAAC,aAAa,CAAC;EAErC;EACAC,MAAM,EAAGC,EAAE,IAAK5B,GAAG,CAAC0B,GAAG,CAAC,eAAeE,EAAE,EAAE,CAAC;EAE5C;EACAC,YAAY,EAAGD,EAAE,IAAK5B,GAAG,CAAC0B,GAAG,CAAC,eAAeE,EAAE,SAAS,CAAC;EAEzD;EACAE,iBAAiB,EAAGF,EAAE,IAAK5B,GAAG,CAAC0B,GAAG,CAAC,eAAeE,EAAE,cAAc,CAAC;EAEnE;EACAG,SAAS,EAAGH,EAAE,IAAK5B,GAAG,CAACwB,IAAI,CAAC,eAAeI,EAAE,SAAS,CAAC;EAEvD;EACAI,aAAa,EAAGC,MAAM,IAAKjC,GAAG,CAAC0B,GAAG,CAAC,oBAAoB,EAAE;IAAEO;EAAO,CAAC,CAAC;EAEpE;EACA;EACAC,QAAQ,EAAEA,CAAA,KAAMlC,GAAG,CAAC0B,GAAG,CAAC,QAAQ,CAAC;EAEjC;EACAS,OAAO,EAAGP,EAAE,IAAK5B,GAAG,CAAC0B,GAAG,CAAC,UAAUE,EAAE,EAAE,CAAC;EAExC;EACAQ,cAAc,EAAGR,EAAE,IAAK5B,GAAG,CAAC0B,GAAG,CAAC,UAAUE,EAAE,UAAU,CAAC;EAEvD;EACAS,UAAU,EAAGT,EAAE,IAAK5B,GAAG,CAACsC,MAAM,CAAC,UAAUV,EAAE,EAAE,CAAC;EAE9C;EACAW,UAAU,EAAEA,CAACC,IAAI,EAAEC,OAAO,GAAG,WAAW,EAAEC,QAAQ,GAAG,IAAI,KAAK;IAC5D,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAAC;IAC7BG,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEJ,OAAO,CAAC;IACnC,IAAIC,QAAQ,EAAE;MACZC,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEH,QAAQ,CAAC;IACxC;IACA,OAAO1C,GAAG,CAACwB,IAAI,CAAC,eAAe,EAAEmB,QAAQ,EAAE;MACzCxC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACA2C,WAAW,EAAEA,CAACC,KAAK,GAAG,IAAI,KAAK/C,GAAG,CAAC0B,GAAG,CAAC,WAAW,EAAE;IAAEO,MAAM,EAAEc,KAAK,GAAG;MAAEA;IAAM,CAAC,GAAG,CAAC;EAAE,CAAC,CAAC;EAEvF;EACAC,SAAS,EAAEA,CAAA,KAAMhD,GAAG,CAAC0B,GAAG,CAAC,SAAS,CAAC;EAEnC;EACA;EACAuB,UAAU,EAAEA,CAAA,KAAMjD,GAAG,CAAC0B,GAAG,CAAC,UAAU,CAAC;EAErC;EACAwB,WAAW,EAAGC,SAAS,IAAKnD,GAAG,CAACwB,IAAI,CAAC,UAAU,EAAE2B,SAAS,CAAC;EAE3D;EACAC,QAAQ,EAAGxB,EAAE,IAAK5B,GAAG,CAAC0B,GAAG,CAAC,YAAYE,EAAE,EAAE,CAAC;EAE3C;EACAyB,WAAW,EAAGzB,EAAE,IAAK5B,GAAG,CAACwB,IAAI,CAAC,YAAYI,EAAE,SAAS,CAAC;EAEtD;EACA0B,WAAW,EAAEA,CAAA,KAAMtD,GAAG,CAAC0B,GAAG,CAAC,SAAS;AACtC,CAAC;AAED,eAAe1B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
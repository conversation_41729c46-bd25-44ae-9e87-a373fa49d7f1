{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Clock6 = createLucideIcon(\"Clock6\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"polyline\", {\n  points: \"12 6 12 12 12 16.5\",\n  key: \"hb2qv6\"\n}]]);\nexport { Clock6 as default };\n//# sourceMappingURL=clock-6.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
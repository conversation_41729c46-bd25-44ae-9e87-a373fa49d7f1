{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst FileCode2 = createLucideIcon(\"FileCode2\", [[\"path\", {\n  d: \"M4 22h14a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v4\",\n  key: \"702lig\"\n}], [\"polyline\", {\n  points: \"14 2 14 8 20 8\",\n  key: \"1ew0cm\"\n}], [\"path\", {\n  d: \"m9 18 3-3-3-3\",\n  key: \"112psh\"\n}], [\"path\", {\n  d: \"m5 12-3 3 3 3\",\n  key: \"oke12k\"\n}]]);\nexport { FileCode2 as default };\n//# sourceMappingURL=file-code-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
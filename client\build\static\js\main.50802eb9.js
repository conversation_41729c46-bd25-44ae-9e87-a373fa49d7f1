/*! For license information please see main.50802eb9.js.LICENSE.txt */
(()=>{"use strict";var e={49:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),o=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,x={};function g(e,t,n){this.props=e,this.context=t,this.refs=x,this.updater=n||m}function y(){}function v(e,t,n){this.props=e,this.context=t,this.refs=x,this.updater=n||m}g.prototype.isReactComponent={},g.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},g.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=g.prototype;var b=v.prototype=new y;b.constructor=v,h(b,g.prototype),b.isPureReactComponent=!0;var w=Array.isArray,j=Object.prototype.hasOwnProperty,N={current:null},k={key:!0,ref:!0,__self:!0,__source:!0};function S(e,t,r){var a,l={},s=null,i=null;if(null!=t)for(a in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(s=""+t.key),t)j.call(t,a)&&!k.hasOwnProperty(a)&&(l[a]=t[a]);var o=arguments.length-2;if(1===o)l.children=r;else if(1<o){for(var c=Array(o),u=0;u<o;u++)c[u]=arguments[u+2];l.children=c}if(e&&e.defaultProps)for(a in o=e.defaultProps)void 0===l[a]&&(l[a]=o[a]);return{$$typeof:n,type:e,key:s,ref:i,props:l,_owner:N.current}}function E(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var C=/\/+/g;function _(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function R(e,t,a,l,s){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var o=!1;if(null===e)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case n:case r:o=!0}}if(o)return s=s(o=e),e=""===l?"."+_(o,0):l,w(s)?(a="",null!=e&&(a=e.replace(C,"$&/")+"/"),R(s,t,a,"",function(e){return e})):null!=s&&(E(s)&&(s=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(s,a+(!s.key||o&&o.key===s.key?"":(""+s.key).replace(C,"$&/")+"/")+e)),t.push(s)),1;if(o=0,l=""===l?".":l+":",w(e))for(var c=0;c<e.length;c++){var u=l+_(i=e[c],c);o+=R(i,t,a,u,s)}else if(u=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof u)for(e=u.call(e),c=0;!(i=e.next()).done;)o+=R(i=i.value,t,a,u=l+_(i,c++),s);else if("object"===i)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function P(e,t,n){if(null==e)return e;var r=[],a=0;return R(e,r,"","",function(e){return t.call(n,e,a++)}),r}function T(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var L={current:null},O={transition:null},F={ReactCurrentDispatcher:L,ReactCurrentBatchConfig:O,ReactCurrentOwner:N};function z(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:P,forEach:function(e,t,n){P(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return P(e,function(){t++}),t},toArray:function(e){return P(e,function(e){return e})||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=g,t.Fragment=a,t.Profiler=s,t.PureComponent=v,t.StrictMode=l,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=F,t.act=z,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=h({},e.props),l=e.key,s=e.ref,i=e._owner;if(null!=t){if(void 0!==t.ref&&(s=t.ref,i=N.current),void 0!==t.key&&(l=""+t.key),e.type&&e.type.defaultProps)var o=e.type.defaultProps;for(c in t)j.call(t,c)&&!k.hasOwnProperty(c)&&(a[c]=void 0===t[c]&&void 0!==o?o[c]:t[c])}var c=arguments.length-2;if(1===c)a.children=r;else if(1<c){o=Array(c);for(var u=0;u<c;u++)o[u]=arguments[u+2];a.children=o}return{$$typeof:n,type:e.type,key:l,ref:s,props:a,_owner:i}},t.createContext=function(e){return(e={$$typeof:o,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:i,_context:e},e.Consumer=e},t.createElement=S,t.createFactory=function(e){var t=S.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=E,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:T}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=O.transition;O.transition={};try{e()}finally{O.transition=t}},t.unstable_act=z,t.useCallback=function(e,t){return L.current.useCallback(e,t)},t.useContext=function(e){return L.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return L.current.useDeferredValue(e)},t.useEffect=function(e,t){return L.current.useEffect(e,t)},t.useId=function(){return L.current.useId()},t.useImperativeHandle=function(e,t,n){return L.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return L.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return L.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return L.current.useMemo(e,t)},t.useReducer=function(e,t,n){return L.current.useReducer(e,t,n)},t.useRef=function(e){return L.current.useRef(e)},t.useState=function(e){return L.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return L.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return L.current.useTransition()},t.version="18.3.1"},119:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(345)},340:(e,t,n)=>{e.exports=n(761)},345:(e,t,n)=>{var r=n(950),a=n(340);function l(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var s=new Set,i={};function o(e,t){c(e,t),c(e+"Capture",t)}function c(e,t){for(i[e]=t,e=0;e<t.length;e++)s.add(t[e])}var u=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},m={};function h(e,t,n,r,a,l,s){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=s}var x={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){x[e]=new h(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];x[t]=new h(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){x[e]=new h(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){x[e]=new h(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){x[e]=new h(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){x[e]=new h(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){x[e]=new h(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){x[e]=new h(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){x[e]=new h(e,5,!1,e.toLowerCase(),null,!1,!1)});var g=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function v(e,t,n,r){var a=x.hasOwnProperty(t)?x[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(m,e)||!d.call(p,e)&&(f.test(e)?m[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(g,y);x[t]=new h(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(g,y);x[t]=new h(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(g,y);x[t]=new h(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){x[e]=new h(e,1,!1,e.toLowerCase(),null,!1,!1)}),x.xlinkHref=new h("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){x[e]=new h(e,1,!1,e.toLowerCase(),null,!0,!0)});var b=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),j=Symbol.for("react.portal"),N=Symbol.for("react.fragment"),k=Symbol.for("react.strict_mode"),S=Symbol.for("react.profiler"),E=Symbol.for("react.provider"),C=Symbol.for("react.context"),_=Symbol.for("react.forward_ref"),R=Symbol.for("react.suspense"),P=Symbol.for("react.suspense_list"),T=Symbol.for("react.memo"),L=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var O=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var F=Symbol.iterator;function z(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=F&&e[F]||e["@@iterator"])?e:null}var M,D=Object.assign;function A(e){if(void 0===M)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);M=t&&t[1]||""}return"\n"+M+e}var U=!1;function I(e,t){if(!e||U)return"";U=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&"string"===typeof c.stack){for(var a=c.stack.split("\n"),l=r.stack.split("\n"),s=a.length-1,i=l.length-1;1<=s&&0<=i&&a[s]!==l[i];)i--;for(;1<=s&&0<=i;s--,i--)if(a[s]!==l[i]){if(1!==s||1!==i)do{if(s--,0>--i||a[s]!==l[i]){var o="\n"+a[s].replace(" at new "," at ");return e.displayName&&o.includes("<anonymous>")&&(o=o.replace("<anonymous>",e.displayName)),o}}while(1<=s&&0<=i);break}}}finally{U=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?A(e):""}function B(e){switch(e.tag){case 5:return A(e.type);case 16:return A("Lazy");case 13:return A("Suspense");case 19:return A("SuspenseList");case 0:case 2:case 15:return e=I(e.type,!1);case 11:return e=I(e.type.render,!1);case 1:return e=I(e.type,!0);default:return""}}function $(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case N:return"Fragment";case j:return"Portal";case S:return"Profiler";case k:return"StrictMode";case R:return"Suspense";case P:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case C:return(e.displayName||"Context")+".Consumer";case E:return(e._context.displayName||"Context")+".Provider";case _:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case T:return null!==(t=e.displayName||null)?t:$(e.type)||"Memo";case L:t=e._payload,e=e._init;try{return $(e(t))}catch(n){}}return null}function V(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return $(t);case 8:return t===k?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function H(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function q(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function W(e){e._valueTracker||(e._valueTracker=function(e){var t=q(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=q(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function J(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function K(e,t){var n=t.checked;return D({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function G(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=H(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function X(e,t){null!=(t=t.checked)&&v(e,"checked",t,!1)}function Y(e,t){X(e,t);var n=H(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,H(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&J(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+H(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(l(91));return D({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(l(92));if(te(n)){if(1<n.length)throw Error(l(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:H(n)}}function le(e,t){var n=H(t.value),r=H(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function se(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function ie(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function oe(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?ie(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ce,ue,de=(ue=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ce=ce||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ce.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction(function(){return ue(e,t)})}:ue);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},me=["Webkit","ms","Moz","O"];function he(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function xe(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=he(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(pe).forEach(function(e){me.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]})});var ge=D({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ye(e,t){if(t){if(ge[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(l(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(l(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(l(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(l(62))}}function ve(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var be=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var je=null,Ne=null,ke=null;function Se(e){if(e=va(e)){if("function"!==typeof je)throw Error(l(280));var t=e.stateNode;t&&(t=wa(t),je(e.stateNode,e.type,t))}}function Ee(e){Ne?ke?ke.push(e):ke=[e]:Ne=e}function Ce(){if(Ne){var e=Ne,t=ke;if(ke=Ne=null,Se(e),t)for(e=0;e<t.length;e++)Se(t[e])}}function _e(e,t){return e(t)}function Re(){}var Pe=!1;function Te(e,t,n){if(Pe)return e(t,n);Pe=!0;try{return _e(e,t,n)}finally{Pe=!1,(null!==Ne||null!==ke)&&(Re(),Ce())}}function Le(e,t){var n=e.stateNode;if(null===n)return null;var r=wa(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(l(231,t,typeof n));return n}var Oe=!1;if(u)try{var Fe={};Object.defineProperty(Fe,"passive",{get:function(){Oe=!0}}),window.addEventListener("test",Fe,Fe),window.removeEventListener("test",Fe,Fe)}catch(ue){Oe=!1}function ze(e,t,n,r,a,l,s,i,o){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(u){this.onError(u)}}var Me=!1,De=null,Ae=!1,Ue=null,Ie={onError:function(e){Me=!0,De=e}};function Be(e,t,n,r,a,l,s,i,o){Me=!1,De=null,ze.apply(Ie,arguments)}function $e(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Ve(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function He(e){if($e(e)!==e)throw Error(l(188))}function qe(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=$e(e)))throw Error(l(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var s=a.alternate;if(null===s){if(null!==(r=a.return)){n=r;continue}break}if(a.child===s.child){for(s=a.child;s;){if(s===n)return He(a),e;if(s===r)return He(a),t;s=s.sibling}throw Error(l(188))}if(n.return!==r.return)n=a,r=s;else{for(var i=!1,o=a.child;o;){if(o===n){i=!0,n=a,r=s;break}if(o===r){i=!0,r=a,n=s;break}o=o.sibling}if(!i){for(o=s.child;o;){if(o===n){i=!0,n=s,r=a;break}if(o===r){i=!0,r=s,n=a;break}o=o.sibling}if(!i)throw Error(l(189))}}if(n.alternate!==r)throw Error(l(190))}if(3!==n.tag)throw Error(l(188));return n.stateNode.current===n?e:t}(e))?We(e):null}function We(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=We(e);if(null!==t)return t;e=e.sibling}return null}var Qe=a.unstable_scheduleCallback,Je=a.unstable_cancelCallback,Ke=a.unstable_shouldYield,Ge=a.unstable_requestPaint,Xe=a.unstable_now,Ye=a.unstable_getCurrentPriorityLevel,Ze=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,lt=null;var st=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(it(e)/ot|0)|0},it=Math.log,ot=Math.LN2;var ct=64,ut=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,l=e.pingedLanes,s=268435455&n;if(0!==s){var i=s&~a;0!==i?r=dt(i):0!==(l&=s)&&(r=dt(l))}else 0!==(s=n&~a)?r=dt(s):0!==l&&(r=dt(l));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(l=t&-t)||16===a&&0!==(4194240&l)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-st(t)),r|=e[n],t&=~a;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function mt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function ht(){var e=ct;return 0===(4194240&(ct<<=1))&&(ct=64),e}function xt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function gt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-st(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-st(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var vt=0;function bt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var wt,jt,Nt,kt,St,Et=!1,Ct=[],_t=null,Rt=null,Pt=null,Tt=new Map,Lt=new Map,Ot=[],Ft="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function zt(e,t){switch(e){case"focusin":case"focusout":_t=null;break;case"dragenter":case"dragleave":Rt=null;break;case"mouseover":case"mouseout":Pt=null;break;case"pointerover":case"pointerout":Tt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Lt.delete(t.pointerId)}}function Mt(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&(null!==(t=va(t))&&jt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function Dt(e){var t=ya(e.target);if(null!==t){var n=$e(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Ve(n)))return e.blockedOn=t,void St(e.priority,function(){Nt(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function At(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Kt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=va(n))&&jt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);be=r,n.target.dispatchEvent(r),be=null,t.shift()}return!0}function Ut(e,t,n){At(e)&&n.delete(t)}function It(){Et=!1,null!==_t&&At(_t)&&(_t=null),null!==Rt&&At(Rt)&&(Rt=null),null!==Pt&&At(Pt)&&(Pt=null),Tt.forEach(Ut),Lt.forEach(Ut)}function Bt(e,t){e.blockedOn===t&&(e.blockedOn=null,Et||(Et=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,It)))}function $t(e){function t(t){return Bt(t,e)}if(0<Ct.length){Bt(Ct[0],e);for(var n=1;n<Ct.length;n++){var r=Ct[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==_t&&Bt(_t,e),null!==Rt&&Bt(Rt,e),null!==Pt&&Bt(Pt,e),Tt.forEach(t),Lt.forEach(t),n=0;n<Ot.length;n++)(r=Ot[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Ot.length&&null===(n=Ot[0]).blockedOn;)Dt(n),null===n.blockedOn&&Ot.shift()}var Vt=b.ReactCurrentBatchConfig,Ht=!0;function qt(e,t,n,r){var a=vt,l=Vt.transition;Vt.transition=null;try{vt=1,Qt(e,t,n,r)}finally{vt=a,Vt.transition=l}}function Wt(e,t,n,r){var a=vt,l=Vt.transition;Vt.transition=null;try{vt=4,Qt(e,t,n,r)}finally{vt=a,Vt.transition=l}}function Qt(e,t,n,r){if(Ht){var a=Kt(e,t,n,r);if(null===a)Hr(e,t,r,Jt,n),zt(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return _t=Mt(_t,e,t,n,r,a),!0;case"dragenter":return Rt=Mt(Rt,e,t,n,r,a),!0;case"mouseover":return Pt=Mt(Pt,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return Tt.set(l,Mt(Tt.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,Lt.set(l,Mt(Lt.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(zt(e,r),4&t&&-1<Ft.indexOf(e)){for(;null!==a;){var l=va(a);if(null!==l&&wt(l),null===(l=Kt(e,t,n,r))&&Hr(e,t,r,Jt,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else Hr(e,t,r,null,n)}}var Jt=null;function Kt(e,t,n,r){if(Jt=null,null!==(e=ya(e=we(r))))if(null===(t=$e(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=Ve(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Jt=e,null}function Gt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ye()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Xt=null,Yt=null,Zt=null;function en(){if(Zt)return Zt;var e,t,n=Yt,r=n.length,a="value"in Xt?Xt.value:Xt.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var s=r-e;for(t=1;t<=s&&n[r-t]===a[l-t];t++);return Zt=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,l){for(var s in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(s)&&(t=e[s],this[s]=t?t(a):a[s]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return D(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var ln,sn,on,cn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},un=an(cn),dn=D({},cn,{view:0,detail:0}),fn=an(dn),pn=D({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Sn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==on&&(on&&"mousemove"===e.type?(ln=e.screenX-on.screenX,sn=e.screenY-on.screenY):sn=ln=0,on=e),ln)},movementY:function(e){return"movementY"in e?e.movementY:sn}}),mn=an(pn),hn=an(D({},pn,{dataTransfer:0})),xn=an(D({},dn,{relatedTarget:0})),gn=an(D({},cn,{animationName:0,elapsedTime:0,pseudoElement:0})),yn=D({},cn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),vn=an(yn),bn=an(D({},cn,{data:0})),wn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},jn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Nn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function kn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Nn[e])&&!!t[e]}function Sn(){return kn}var En=D({},dn,{key:function(e){if(e.key){var t=wn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?jn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Sn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Cn=an(En),_n=an(D({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Rn=an(D({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Sn})),Pn=an(D({},cn,{propertyName:0,elapsedTime:0,pseudoElement:0})),Tn=D({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Ln=an(Tn),On=[9,13,27,32],Fn=u&&"CompositionEvent"in window,zn=null;u&&"documentMode"in document&&(zn=document.documentMode);var Mn=u&&"TextEvent"in window&&!zn,Dn=u&&(!Fn||zn&&8<zn&&11>=zn),An=String.fromCharCode(32),Un=!1;function In(e,t){switch(e){case"keyup":return-1!==On.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Bn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var $n=!1;var Vn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Hn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Vn[e.type]:"textarea"===t}function qn(e,t,n,r){Ee(r),0<(t=Wr(t,"onChange")).length&&(n=new un("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Wn=null,Qn=null;function Jn(e){Ar(e,0)}function Kn(e){if(Q(ba(e)))return e}function Gn(e,t){if("change"===e)return t}var Xn=!1;if(u){var Yn;if(u){var Zn="oninput"in document;if(!Zn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Zn="function"===typeof er.oninput}Yn=Zn}else Yn=!1;Xn=Yn&&(!document.documentMode||9<document.documentMode)}function tr(){Wn&&(Wn.detachEvent("onpropertychange",nr),Qn=Wn=null)}function nr(e){if("value"===e.propertyName&&Kn(Qn)){var t=[];qn(t,Qn,e,we(e)),Te(Jn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Qn=n,(Wn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Kn(Qn)}function lr(e,t){if("click"===e)return Kn(t)}function sr(e,t){if("input"===e||"change"===e)return Kn(t)}var ir="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function or(e,t){if(ir(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!ir(e[a],t[a]))return!1}return!0}function cr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ur(e,t){var n,r=cr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=cr(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=J();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=J((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function mr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,l=Math.min(r.start,a);r=void 0===r.end?l:Math.min(r.end,a),!e.extend&&l>r&&(a=r,r=l,l=a),a=ur(n,l);var s=ur(n,r);a&&s&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var hr=u&&"documentMode"in document&&11>=document.documentMode,xr=null,gr=null,yr=null,vr=!1;function br(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;vr||null==xr||xr!==J(r)||("selectionStart"in(r=xr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},yr&&or(yr,r)||(yr=r,0<(r=Wr(gr,"onSelect")).length&&(t=new un("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=xr)))}function wr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var jr={animationend:wr("Animation","AnimationEnd"),animationiteration:wr("Animation","AnimationIteration"),animationstart:wr("Animation","AnimationStart"),transitionend:wr("Transition","TransitionEnd")},Nr={},kr={};function Sr(e){if(Nr[e])return Nr[e];if(!jr[e])return e;var t,n=jr[e];for(t in n)if(n.hasOwnProperty(t)&&t in kr)return Nr[e]=n[t];return e}u&&(kr=document.createElement("div").style,"AnimationEvent"in window||(delete jr.animationend.animation,delete jr.animationiteration.animation,delete jr.animationstart.animation),"TransitionEvent"in window||delete jr.transitionend.transition);var Er=Sr("animationend"),Cr=Sr("animationiteration"),_r=Sr("animationstart"),Rr=Sr("transitionend"),Pr=new Map,Tr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Lr(e,t){Pr.set(e,t),o(t,[e])}for(var Or=0;Or<Tr.length;Or++){var Fr=Tr[Or];Lr(Fr.toLowerCase(),"on"+(Fr[0].toUpperCase()+Fr.slice(1)))}Lr(Er,"onAnimationEnd"),Lr(Cr,"onAnimationIteration"),Lr(_r,"onAnimationStart"),Lr("dblclick","onDoubleClick"),Lr("focusin","onFocus"),Lr("focusout","onBlur"),Lr(Rr,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),o("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),o("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),o("onBeforeInput",["compositionend","keypress","textInput","paste"]),o("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),o("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),o("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Mr=new Set("cancel close invalid load scroll toggle".split(" ").concat(zr));function Dr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,s,i,o,c){if(Be.apply(this,arguments),Me){if(!Me)throw Error(l(198));var u=De;Me=!1,De=null,Ae||(Ae=!0,Ue=u)}}(r,t,void 0,e),e.currentTarget=null}function Ar(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var s=r.length-1;0<=s;s--){var i=r[s],o=i.instance,c=i.currentTarget;if(i=i.listener,o!==l&&a.isPropagationStopped())break e;Dr(a,i,c),l=o}else for(s=0;s<r.length;s++){if(o=(i=r[s]).instance,c=i.currentTarget,i=i.listener,o!==l&&a.isPropagationStopped())break e;Dr(a,i,c),l=o}}}if(Ae)throw e=Ue,Ae=!1,Ue=null,e}function Ur(e,t){var n=t[ha];void 0===n&&(n=t[ha]=new Set);var r=e+"__bubble";n.has(r)||(Vr(t,e,2,!1),n.add(r))}function Ir(e,t,n){var r=0;t&&(r|=4),Vr(n,e,r,t)}var Br="_reactListening"+Math.random().toString(36).slice(2);function $r(e){if(!e[Br]){e[Br]=!0,s.forEach(function(t){"selectionchange"!==t&&(Mr.has(t)||Ir(t,!1,e),Ir(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Br]||(t[Br]=!0,Ir("selectionchange",!1,t))}}function Vr(e,t,n,r){switch(Gt(t)){case 1:var a=qt;break;case 4:a=Wt;break;default:a=Qt}n=a.bind(null,t,n,e),a=void 0,!Oe||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Hr(e,t,n,r,a){var l=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var s=r.tag;if(3===s||4===s){var i=r.stateNode.containerInfo;if(i===a||8===i.nodeType&&i.parentNode===a)break;if(4===s)for(s=r.return;null!==s;){var o=s.tag;if((3===o||4===o)&&((o=s.stateNode.containerInfo)===a||8===o.nodeType&&o.parentNode===a))return;s=s.return}for(;null!==i;){if(null===(s=ya(i)))return;if(5===(o=s.tag)||6===o){r=l=s;continue e}i=i.parentNode}}r=r.return}Te(function(){var r=l,a=we(n),s=[];e:{var i=Pr.get(e);if(void 0!==i){var o=un,c=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":o=Cn;break;case"focusin":c="focus",o=xn;break;case"focusout":c="blur",o=xn;break;case"beforeblur":case"afterblur":o=xn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":o=mn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":o=hn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":o=Rn;break;case Er:case Cr:case _r:o=gn;break;case Rr:o=Pn;break;case"scroll":o=fn;break;case"wheel":o=Ln;break;case"copy":case"cut":case"paste":o=vn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":o=_n}var u=0!==(4&t),d=!u&&"scroll"===e,f=u?null!==i?i+"Capture":null:i;u=[];for(var p,m=r;null!==m;){var h=(p=m).stateNode;if(5===p.tag&&null!==h&&(p=h,null!==f&&(null!=(h=Le(m,f))&&u.push(qr(m,h,p)))),d)break;m=m.return}0<u.length&&(i=new o(i,c,null,n,a),s.push({event:i,listeners:u}))}}if(0===(7&t)){if(o="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===be||!(c=n.relatedTarget||n.fromElement)||!ya(c)&&!c[ma])&&(o||i)&&(i=a.window===a?a:(i=a.ownerDocument)?i.defaultView||i.parentWindow:window,o?(o=r,null!==(c=(c=n.relatedTarget||n.toElement)?ya(c):null)&&(c!==(d=$e(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(o=null,c=r),o!==c)){if(u=mn,h="onMouseLeave",f="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(u=_n,h="onPointerLeave",f="onPointerEnter",m="pointer"),d=null==o?i:ba(o),p=null==c?i:ba(c),(i=new u(h,m+"leave",o,n,a)).target=d,i.relatedTarget=p,h=null,ya(a)===r&&((u=new u(f,m+"enter",c,n,a)).target=p,u.relatedTarget=d,h=u),d=h,o&&c)e:{for(f=c,m=0,p=u=o;p;p=Qr(p))m++;for(p=0,h=f;h;h=Qr(h))p++;for(;0<m-p;)u=Qr(u),m--;for(;0<p-m;)f=Qr(f),p--;for(;m--;){if(u===f||null!==f&&u===f.alternate)break e;u=Qr(u),f=Qr(f)}u=null}else u=null;null!==o&&Jr(s,i,o,u,!1),null!==c&&null!==d&&Jr(s,d,c,u,!0)}if("select"===(o=(i=r?ba(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===o&&"file"===i.type)var x=Gn;else if(Hn(i))if(Xn)x=sr;else{x=ar;var g=rr}else(o=i.nodeName)&&"input"===o.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(x=lr);switch(x&&(x=x(e,r))?qn(s,x,n,a):(g&&g(e,i,r),"focusout"===e&&(g=i._wrapperState)&&g.controlled&&"number"===i.type&&ee(i,"number",i.value)),g=r?ba(r):window,e){case"focusin":(Hn(g)||"true"===g.contentEditable)&&(xr=g,gr=r,yr=null);break;case"focusout":yr=gr=xr=null;break;case"mousedown":vr=!0;break;case"contextmenu":case"mouseup":case"dragend":vr=!1,br(s,n,a);break;case"selectionchange":if(hr)break;case"keydown":case"keyup":br(s,n,a)}var y;if(Fn)e:{switch(e){case"compositionstart":var v="onCompositionStart";break e;case"compositionend":v="onCompositionEnd";break e;case"compositionupdate":v="onCompositionUpdate";break e}v=void 0}else $n?In(e,n)&&(v="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(v="onCompositionStart");v&&(Dn&&"ko"!==n.locale&&($n||"onCompositionStart"!==v?"onCompositionEnd"===v&&$n&&(y=en()):(Yt="value"in(Xt=a)?Xt.value:Xt.textContent,$n=!0)),0<(g=Wr(r,v)).length&&(v=new bn(v,e,null,n,a),s.push({event:v,listeners:g}),y?v.data=y:null!==(y=Bn(n))&&(v.data=y))),(y=Mn?function(e,t){switch(e){case"compositionend":return Bn(t);case"keypress":return 32!==t.which?null:(Un=!0,An);case"textInput":return(e=t.data)===An&&Un?null:e;default:return null}}(e,n):function(e,t){if($n)return"compositionend"===e||!Fn&&In(e,t)?(e=en(),Zt=Yt=Xt=null,$n=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Dn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Wr(r,"onBeforeInput")).length&&(a=new bn("onBeforeInput","beforeinput",null,n,a),s.push({event:a,listeners:r}),a.data=y))}Ar(s,t)})}function qr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Wr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;5===a.tag&&null!==l&&(a=l,null!=(l=Le(e,n))&&r.unshift(qr(e,l,a)),null!=(l=Le(e,t))&&r.push(qr(e,l,a))),e=e.return}return r}function Qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Jr(e,t,n,r,a){for(var l=t._reactName,s=[];null!==n&&n!==r;){var i=n,o=i.alternate,c=i.stateNode;if(null!==o&&o===r)break;5===i.tag&&null!==c&&(i=c,a?null!=(o=Le(n,l))&&s.unshift(qr(n,o,i)):a||null!=(o=Le(n,l))&&s.push(qr(n,o,i))),n=n.return}0!==s.length&&e.push({event:t,listeners:s})}var Kr=/\r\n?/g,Gr=/\u0000|\uFFFD/g;function Xr(e){return("string"===typeof e?e:""+e).replace(Kr,"\n").replace(Gr,"")}function Yr(e,t,n){if(t=Xr(t),Xr(e)!==t&&n)throw Error(l(425))}function Zr(){}var ea=null,ta=null;function na(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ra="function"===typeof setTimeout?setTimeout:void 0,aa="function"===typeof clearTimeout?clearTimeout:void 0,la="function"===typeof Promise?Promise:void 0,sa="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof la?function(e){return la.resolve(null).then(e).catch(ia)}:ra;function ia(e){setTimeout(function(){throw e})}function oa(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void $t(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);$t(t)}function ca(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ua(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactFiber$"+da,pa="__reactProps$"+da,ma="__reactContainer$"+da,ha="__reactEvents$"+da,xa="__reactListeners$"+da,ga="__reactHandles$"+da;function ya(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ma]||n[fa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ua(e);null!==e;){if(n=e[fa])return n;e=ua(e)}return t}n=(e=n).parentNode}return null}function va(e){return!(e=e[fa]||e[ma])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function ba(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(l(33))}function wa(e){return e[pa]||null}var ja=[],Na=-1;function ka(e){return{current:e}}function Sa(e){0>Na||(e.current=ja[Na],ja[Na]=null,Na--)}function Ea(e,t){Na++,ja[Na]=e.current,e.current=t}var Ca={},_a=ka(Ca),Ra=ka(!1),Pa=Ca;function Ta(e,t){var n=e.type.contextTypes;if(!n)return Ca;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,l={};for(a in n)l[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function La(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Oa(){Sa(Ra),Sa(_a)}function Fa(e,t,n){if(_a.current!==Ca)throw Error(l(168));Ea(_a,t),Ea(Ra,n)}function za(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(l(108,V(e)||"Unknown",a));return D({},n,r)}function Ma(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ca,Pa=_a.current,Ea(_a,e),Ea(Ra,Ra.current),!0}function Da(e,t,n){var r=e.stateNode;if(!r)throw Error(l(169));n?(e=za(e,t,Pa),r.__reactInternalMemoizedMergedChildContext=e,Sa(Ra),Sa(_a),Ea(_a,e)):Sa(Ra),Ea(Ra,n)}var Aa=null,Ua=!1,Ia=!1;function Ba(e){null===Aa?Aa=[e]:Aa.push(e)}function $a(){if(!Ia&&null!==Aa){Ia=!0;var e=0,t=vt;try{var n=Aa;for(vt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Aa=null,Ua=!1}catch(a){throw null!==Aa&&(Aa=Aa.slice(e+1)),Qe(Ze,$a),a}finally{vt=t,Ia=!1}}return null}var Va=[],Ha=0,qa=null,Wa=0,Qa=[],Ja=0,Ka=null,Ga=1,Xa="";function Ya(e,t){Va[Ha++]=Wa,Va[Ha++]=qa,qa=e,Wa=t}function Za(e,t,n){Qa[Ja++]=Ga,Qa[Ja++]=Xa,Qa[Ja++]=Ka,Ka=e;var r=Ga;e=Xa;var a=32-st(r)-1;r&=~(1<<a),n+=1;var l=32-st(t)+a;if(30<l){var s=a-a%5;l=(r&(1<<s)-1).toString(32),r>>=s,a-=s,Ga=1<<32-st(t)+a|n<<a|r,Xa=l+e}else Ga=1<<l|n<<a|r,Xa=e}function el(e){null!==e.return&&(Ya(e,1),Za(e,1,0))}function tl(e){for(;e===qa;)qa=Va[--Ha],Va[Ha]=null,Wa=Va[--Ha],Va[Ha]=null;for(;e===Ka;)Ka=Qa[--Ja],Qa[Ja]=null,Xa=Qa[--Ja],Qa[Ja]=null,Ga=Qa[--Ja],Qa[Ja]=null}var nl=null,rl=null,al=!1,ll=null;function sl(e,t){var n=Tc(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function il(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,nl=e,rl=ca(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,nl=e,rl=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ka?{id:Ga,overflow:Xa}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Tc(18,null,null,0)).stateNode=t,n.return=e,e.child=n,nl=e,rl=null,!0);default:return!1}}function ol(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function cl(e){if(al){var t=rl;if(t){var n=t;if(!il(e,t)){if(ol(e))throw Error(l(418));t=ca(n.nextSibling);var r=nl;t&&il(e,t)?sl(r,n):(e.flags=-4097&e.flags|2,al=!1,nl=e)}}else{if(ol(e))throw Error(l(418));e.flags=-4097&e.flags|2,al=!1,nl=e}}}function ul(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;nl=e}function dl(e){if(e!==nl)return!1;if(!al)return ul(e),al=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!na(e.type,e.memoizedProps)),t&&(t=rl)){if(ol(e))throw fl(),Error(l(418));for(;t;)sl(e,t),t=ca(t.nextSibling)}if(ul(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(l(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){rl=ca(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}rl=null}}else rl=nl?ca(e.stateNode.nextSibling):null;return!0}function fl(){for(var e=rl;e;)e=ca(e.nextSibling)}function pl(){rl=nl=null,al=!1}function ml(e){null===ll?ll=[e]:ll.push(e)}var hl=b.ReactCurrentBatchConfig;function xl(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(l(309));var r=n.stateNode}if(!r)throw Error(l(147,e));var a=r,s=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===s?t.ref:(t=function(e){var t=a.refs;null===e?delete t[s]:t[s]=e},t._stringRef=s,t)}if("string"!==typeof e)throw Error(l(284));if(!n._owner)throw Error(l(290,e))}return e}function gl(e,t){throw e=Object.prototype.toString.call(t),Error(l(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function yl(e){return(0,e._init)(e._payload)}function vl(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Oc(e,t)).index=0,e.sibling=null,e}function s(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=2),t}function o(e,t,n,r){return null===t||6!==t.tag?((t=Dc(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function c(e,t,n,r){var l=n.type;return l===N?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===l||"object"===typeof l&&null!==l&&l.$$typeof===L&&yl(l)===t.type)?((r=a(t,n.props)).ref=xl(e,t,n),r.return=e,r):((r=Fc(n.type,n.key,n.props,null,e.mode,r)).ref=xl(e,t,n),r.return=e,r)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Ac(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,l){return null===t||7!==t.tag?((t=zc(n,e.mode,r,l)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Dc(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=Fc(t.type,t.key,t.props,null,e.mode,n)).ref=xl(e,null,t),n.return=e,n;case j:return(t=Ac(t,e.mode,n)).return=e,t;case L:return f(e,(0,t._init)(t._payload),n)}if(te(t)||z(t))return(t=zc(t,e.mode,n,null)).return=e,t;gl(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:o(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===a?c(e,t,n,r):null;case j:return n.key===a?u(e,t,n,r):null;case L:return p(e,t,(a=n._init)(n._payload),r)}if(te(n)||z(n))return null!==a?null:d(e,t,n,r,null);gl(e,n)}return null}function m(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return o(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case j:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case L:return m(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||z(r))return d(t,e=e.get(n)||null,r,a,null);gl(t,r)}return null}function h(a,l,i,o){for(var c=null,u=null,d=l,h=l=0,x=null;null!==d&&h<i.length;h++){d.index>h?(x=d,d=null):x=d.sibling;var g=p(a,d,i[h],o);if(null===g){null===d&&(d=x);break}e&&d&&null===g.alternate&&t(a,d),l=s(g,l,h),null===u?c=g:u.sibling=g,u=g,d=x}if(h===i.length)return n(a,d),al&&Ya(a,h),c;if(null===d){for(;h<i.length;h++)null!==(d=f(a,i[h],o))&&(l=s(d,l,h),null===u?c=d:u.sibling=d,u=d);return al&&Ya(a,h),c}for(d=r(a,d);h<i.length;h++)null!==(x=m(d,a,h,i[h],o))&&(e&&null!==x.alternate&&d.delete(null===x.key?h:x.key),l=s(x,l,h),null===u?c=x:u.sibling=x,u=x);return e&&d.forEach(function(e){return t(a,e)}),al&&Ya(a,h),c}function x(a,i,o,c){var u=z(o);if("function"!==typeof u)throw Error(l(150));if(null==(o=u.call(o)))throw Error(l(151));for(var d=u=null,h=i,x=i=0,g=null,y=o.next();null!==h&&!y.done;x++,y=o.next()){h.index>x?(g=h,h=null):g=h.sibling;var v=p(a,h,y.value,c);if(null===v){null===h&&(h=g);break}e&&h&&null===v.alternate&&t(a,h),i=s(v,i,x),null===d?u=v:d.sibling=v,d=v,h=g}if(y.done)return n(a,h),al&&Ya(a,x),u;if(null===h){for(;!y.done;x++,y=o.next())null!==(y=f(a,y.value,c))&&(i=s(y,i,x),null===d?u=y:d.sibling=y,d=y);return al&&Ya(a,x),u}for(h=r(a,h);!y.done;x++,y=o.next())null!==(y=m(h,a,x,y.value,c))&&(e&&null!==y.alternate&&h.delete(null===y.key?x:y.key),i=s(y,i,x),null===d?u=y:d.sibling=y,d=y);return e&&h.forEach(function(e){return t(a,e)}),al&&Ya(a,x),u}return function e(r,l,s,o){if("object"===typeof s&&null!==s&&s.type===N&&null===s.key&&(s=s.props.children),"object"===typeof s&&null!==s){switch(s.$$typeof){case w:e:{for(var c=s.key,u=l;null!==u;){if(u.key===c){if((c=s.type)===N){if(7===u.tag){n(r,u.sibling),(l=a(u,s.props.children)).return=r,r=l;break e}}else if(u.elementType===c||"object"===typeof c&&null!==c&&c.$$typeof===L&&yl(c)===u.type){n(r,u.sibling),(l=a(u,s.props)).ref=xl(r,u,s),l.return=r,r=l;break e}n(r,u);break}t(r,u),u=u.sibling}s.type===N?((l=zc(s.props.children,r.mode,o,s.key)).return=r,r=l):((o=Fc(s.type,s.key,s.props,null,r.mode,o)).ref=xl(r,l,s),o.return=r,r=o)}return i(r);case j:e:{for(u=s.key;null!==l;){if(l.key===u){if(4===l.tag&&l.stateNode.containerInfo===s.containerInfo&&l.stateNode.implementation===s.implementation){n(r,l.sibling),(l=a(l,s.children||[])).return=r,r=l;break e}n(r,l);break}t(r,l),l=l.sibling}(l=Ac(s,r.mode,o)).return=r,r=l}return i(r);case L:return e(r,l,(u=s._init)(s._payload),o)}if(te(s))return h(r,l,s,o);if(z(s))return x(r,l,s,o);gl(r,s)}return"string"===typeof s&&""!==s||"number"===typeof s?(s=""+s,null!==l&&6===l.tag?(n(r,l.sibling),(l=a(l,s)).return=r,r=l):(n(r,l),(l=Dc(s,r.mode,o)).return=r,r=l),i(r)):n(r,l)}}var bl=vl(!0),wl=vl(!1),jl=ka(null),Nl=null,kl=null,Sl=null;function El(){Sl=kl=Nl=null}function Cl(e){var t=jl.current;Sa(jl),e._currentValue=t}function _l(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Rl(e,t){Nl=e,Sl=kl=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(yi=!0),e.firstContext=null)}function Pl(e){var t=e._currentValue;if(Sl!==e)if(e={context:e,memoizedValue:t,next:null},null===kl){if(null===Nl)throw Error(l(308));kl=e,Nl.dependencies={lanes:0,firstContext:e}}else kl=kl.next=e;return t}var Tl=null;function Ll(e){null===Tl?Tl=[e]:Tl.push(e)}function Ol(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Ll(t)):(n.next=a.next,a.next=n),t.interleaved=n,Fl(e,r)}function Fl(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var zl=!1;function Ml(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Dl(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Al(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ul(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&_o)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Fl(e,n)}return null===(a=r.interleaved)?(t.next=t,Ll(r)):(t.next=a.next,a.next=t),r.interleaved=t,Fl(e,n)}function Il(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function Bl(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===l?a=l=s:l=l.next=s,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function $l(e,t,n,r){var a=e.updateQueue;zl=!1;var l=a.firstBaseUpdate,s=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var o=i,c=o.next;o.next=null,null===s?l=c:s.next=c,s=o;var u=e.alternate;null!==u&&((i=(u=u.updateQueue).lastBaseUpdate)!==s&&(null===i?u.firstBaseUpdate=c:i.next=c,u.lastBaseUpdate=o))}if(null!==l){var d=a.baseState;for(s=0,u=c=o=null,i=l;;){var f=i.lane,p=i.eventTime;if((r&f)===f){null!==u&&(u=u.next={eventTime:p,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var m=e,h=i;switch(f=t,p=n,h.tag){case 1:if("function"===typeof(m=h.payload)){d=m.call(p,d,f);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null===(f="function"===typeof(m=h.payload)?m.call(p,d,f):m)||void 0===f)break e;d=D({},d,f);break e;case 2:zl=!0}}null!==i.callback&&0!==i.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[i]:f.push(i))}else p={eventTime:p,lane:f,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===u?(c=u=p,o=d):u=u.next=p,s|=f;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(f=i).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===u&&(o=d),a.baseState=o,a.firstBaseUpdate=c,a.lastBaseUpdate=u,null!==(t=a.shared.interleaved)){a=t;do{s|=a.lane,a=a.next}while(a!==t)}else null===l&&(a.shared.lanes=0);Mo|=s,e.lanes=s,e.memoizedState=d}}function Vl(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(l(191,a));a.call(r)}}}var Hl={},ql=ka(Hl),Wl=ka(Hl),Ql=ka(Hl);function Jl(e){if(e===Hl)throw Error(l(174));return e}function Kl(e,t){switch(Ea(Ql,t),Ea(Wl,e),Ea(ql,Hl),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:oe(null,"");break;default:t=oe(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Sa(ql),Ea(ql,t)}function Gl(){Sa(ql),Sa(Wl),Sa(Ql)}function Xl(e){Jl(Ql.current);var t=Jl(ql.current),n=oe(t,e.type);t!==n&&(Ea(Wl,e),Ea(ql,n))}function Yl(e){Wl.current===e&&(Sa(ql),Sa(Wl))}var Zl=ka(0);function es(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ts=[];function ns(){for(var e=0;e<ts.length;e++)ts[e]._workInProgressVersionPrimary=null;ts.length=0}var rs=b.ReactCurrentDispatcher,as=b.ReactCurrentBatchConfig,ls=0,ss=null,is=null,os=null,cs=!1,us=!1,ds=0,fs=0;function ps(){throw Error(l(321))}function ms(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ir(e[n],t[n]))return!1;return!0}function hs(e,t,n,r,a,s){if(ls=s,ss=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,rs.current=null===e||null===e.memoizedState?Ys:Zs,e=n(r,a),us){s=0;do{if(us=!1,ds=0,25<=s)throw Error(l(301));s+=1,os=is=null,t.updateQueue=null,rs.current=ei,e=n(r,a)}while(us)}if(rs.current=Xs,t=null!==is&&null!==is.next,ls=0,os=is=ss=null,cs=!1,t)throw Error(l(300));return e}function xs(){var e=0!==ds;return ds=0,e}function gs(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===os?ss.memoizedState=os=e:os=os.next=e,os}function ys(){if(null===is){var e=ss.alternate;e=null!==e?e.memoizedState:null}else e=is.next;var t=null===os?ss.memoizedState:os.next;if(null!==t)os=t,is=e;else{if(null===e)throw Error(l(310));e={memoizedState:(is=e).memoizedState,baseState:is.baseState,baseQueue:is.baseQueue,queue:is.queue,next:null},null===os?ss.memoizedState=os=e:os=os.next=e}return os}function vs(e,t){return"function"===typeof t?t(e):t}function bs(e){var t=ys(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=is,a=r.baseQueue,s=n.pending;if(null!==s){if(null!==a){var i=a.next;a.next=s.next,s.next=i}r.baseQueue=a=s,n.pending=null}if(null!==a){s=a.next,r=r.baseState;var o=i=null,c=null,u=s;do{var d=u.lane;if((ls&d)===d)null!==c&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===c?(o=c=f,i=r):c=c.next=f,ss.lanes|=d,Mo|=d}u=u.next}while(null!==u&&u!==s);null===c?i=r:c.next=o,ir(r,t.memoizedState)||(yi=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=c,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{s=a.lane,ss.lanes|=s,Mo|=s,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ws(e){var t=ys(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,s=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{s=e(s,i.action),i=i.next}while(i!==a);ir(s,t.memoizedState)||(yi=!0),t.memoizedState=s,null===t.baseQueue&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function js(){}function Ns(e,t){var n=ss,r=ys(),a=t(),s=!ir(r.memoizedState,a);if(s&&(r.memoizedState=a,yi=!0),r=r.queue,zs(Es.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||null!==os&&1&os.memoizedState.tag){if(n.flags|=2048,Ps(9,Ss.bind(null,n,r,a,t),void 0,null),null===Ro)throw Error(l(349));0!==(30&ls)||ks(n,t,a)}return a}function ks(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=ss.updateQueue)?(t={lastEffect:null,stores:null},ss.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ss(e,t,n,r){t.value=n,t.getSnapshot=r,Cs(t)&&_s(e)}function Es(e,t,n){return n(function(){Cs(t)&&_s(e)})}function Cs(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ir(e,n)}catch(r){return!0}}function _s(e){var t=Fl(e,1);null!==t&&nc(t,e,1,-1)}function Rs(e){var t=gs();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:vs,lastRenderedState:e},t.queue=e,e=e.dispatch=Qs.bind(null,ss,e),[t.memoizedState,e]}function Ps(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=ss.updateQueue)?(t={lastEffect:null,stores:null},ss.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ts(){return ys().memoizedState}function Ls(e,t,n,r){var a=gs();ss.flags|=e,a.memoizedState=Ps(1|t,n,void 0,void 0===r?null:r)}function Os(e,t,n,r){var a=ys();r=void 0===r?null:r;var l=void 0;if(null!==is){var s=is.memoizedState;if(l=s.destroy,null!==r&&ms(r,s.deps))return void(a.memoizedState=Ps(t,n,l,r))}ss.flags|=e,a.memoizedState=Ps(1|t,n,l,r)}function Fs(e,t){return Ls(8390656,8,e,t)}function zs(e,t){return Os(2048,8,e,t)}function Ms(e,t){return Os(4,2,e,t)}function Ds(e,t){return Os(4,4,e,t)}function As(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Us(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Os(4,4,As.bind(null,t,e),n)}function Is(){}function Bs(e,t){var n=ys();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ms(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function $s(e,t){var n=ys();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ms(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Vs(e,t,n){return 0===(21&ls)?(e.baseState&&(e.baseState=!1,yi=!0),e.memoizedState=n):(ir(n,t)||(n=ht(),ss.lanes|=n,Mo|=n,e.baseState=!0),t)}function Hs(e,t){var n=vt;vt=0!==n&&4>n?n:4,e(!0);var r=as.transition;as.transition={};try{e(!1),t()}finally{vt=n,as.transition=r}}function qs(){return ys().memoizedState}function Ws(e,t,n){var r=tc(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Js(e))Ks(t,n);else if(null!==(n=Ol(e,t,n,r))){nc(n,e,r,ec()),Gs(n,t,r)}}function Qs(e,t,n){var r=tc(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Js(e))Ks(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var s=t.lastRenderedState,i=l(s,n);if(a.hasEagerState=!0,a.eagerState=i,ir(i,s)){var o=t.interleaved;return null===o?(a.next=a,Ll(t)):(a.next=o.next,o.next=a),void(t.interleaved=a)}}catch(c){}null!==(n=Ol(e,t,a,r))&&(nc(n,e,r,a=ec()),Gs(n,t,r))}}function Js(e){var t=e.alternate;return e===ss||null!==t&&t===ss}function Ks(e,t){us=cs=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Gs(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var Xs={readContext:Pl,useCallback:ps,useContext:ps,useEffect:ps,useImperativeHandle:ps,useInsertionEffect:ps,useLayoutEffect:ps,useMemo:ps,useReducer:ps,useRef:ps,useState:ps,useDebugValue:ps,useDeferredValue:ps,useTransition:ps,useMutableSource:ps,useSyncExternalStore:ps,useId:ps,unstable_isNewReconciler:!1},Ys={readContext:Pl,useCallback:function(e,t){return gs().memoizedState=[e,void 0===t?null:t],e},useContext:Pl,useEffect:Fs,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ls(4194308,4,As.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ls(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ls(4,2,e,t)},useMemo:function(e,t){var n=gs();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=gs();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Ws.bind(null,ss,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},gs().memoizedState=e},useState:Rs,useDebugValue:Is,useDeferredValue:function(e){return gs().memoizedState=e},useTransition:function(){var e=Rs(!1),t=e[0];return e=Hs.bind(null,e[1]),gs().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ss,a=gs();if(al){if(void 0===n)throw Error(l(407));n=n()}else{if(n=t(),null===Ro)throw Error(l(349));0!==(30&ls)||ks(r,t,n)}a.memoizedState=n;var s={value:n,getSnapshot:t};return a.queue=s,Fs(Es.bind(null,r,s,e),[e]),r.flags|=2048,Ps(9,Ss.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=gs(),t=Ro.identifierPrefix;if(al){var n=Xa;t=":"+t+"R"+(n=(Ga&~(1<<32-st(Ga)-1)).toString(32)+n),0<(n=ds++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=fs++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Zs={readContext:Pl,useCallback:Bs,useContext:Pl,useEffect:zs,useImperativeHandle:Us,useInsertionEffect:Ms,useLayoutEffect:Ds,useMemo:$s,useReducer:bs,useRef:Ts,useState:function(){return bs(vs)},useDebugValue:Is,useDeferredValue:function(e){return Vs(ys(),is.memoizedState,e)},useTransition:function(){return[bs(vs)[0],ys().memoizedState]},useMutableSource:js,useSyncExternalStore:Ns,useId:qs,unstable_isNewReconciler:!1},ei={readContext:Pl,useCallback:Bs,useContext:Pl,useEffect:zs,useImperativeHandle:Us,useInsertionEffect:Ms,useLayoutEffect:Ds,useMemo:$s,useReducer:ws,useRef:Ts,useState:function(){return ws(vs)},useDebugValue:Is,useDeferredValue:function(e){var t=ys();return null===is?t.memoizedState=e:Vs(t,is.memoizedState,e)},useTransition:function(){return[ws(vs)[0],ys().memoizedState]},useMutableSource:js,useSyncExternalStore:Ns,useId:qs,unstable_isNewReconciler:!1};function ti(e,t){if(e&&e.defaultProps){for(var n in t=D({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function ni(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:D({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ri={isMounted:function(e){return!!(e=e._reactInternals)&&$e(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ec(),a=tc(e),l=Al(r,a);l.payload=t,void 0!==n&&null!==n&&(l.callback=n),null!==(t=Ul(e,l,a))&&(nc(t,e,a,r),Il(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ec(),a=tc(e),l=Al(r,a);l.tag=1,l.payload=t,void 0!==n&&null!==n&&(l.callback=n),null!==(t=Ul(e,l,a))&&(nc(t,e,a,r),Il(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ec(),r=tc(e),a=Al(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Ul(e,a,r))&&(nc(t,e,r,n),Il(t,e,r))}};function ai(e,t,n,r,a,l,s){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,s):!t.prototype||!t.prototype.isPureReactComponent||(!or(n,r)||!or(a,l))}function li(e,t,n){var r=!1,a=Ca,l=t.contextType;return"object"===typeof l&&null!==l?l=Pl(l):(a=La(t)?Pa:_a.current,l=(r=null!==(r=t.contextTypes)&&void 0!==r)?Ta(e,a):Ca),t=new t(n,l),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ri,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=l),t}function si(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ri.enqueueReplaceState(t,t.state,null)}function ii(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Ml(e);var l=t.contextType;"object"===typeof l&&null!==l?a.context=Pl(l):(l=La(t)?Pa:_a.current,a.context=Ta(e,l)),a.state=e.memoizedState,"function"===typeof(l=t.getDerivedStateFromProps)&&(ni(e,t,l,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&ri.enqueueReplaceState(a,a.state,null),$l(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function oi(e,t){try{var n="",r=t;do{n+=B(r),r=r.return}while(r);var a=n}catch(l){a="\nError generating stack: "+l.message+"\n"+l.stack}return{value:e,source:t,stack:a,digest:null}}function ci(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function ui(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var di="function"===typeof WeakMap?WeakMap:Map;function fi(e,t,n){(n=Al(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ho||(Ho=!0,qo=r),ui(0,t)},n}function pi(e,t,n){(n=Al(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){ui(0,t)}}var l=e.stateNode;return null!==l&&"function"===typeof l.componentDidCatch&&(n.callback=function(){ui(0,t),"function"!==typeof r&&(null===Wo?Wo=new Set([this]):Wo.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function mi(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new di;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Sc.bind(null,e,t,n),t.then(e,e))}function hi(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function xi(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Al(-1,1)).tag=2,Ul(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var gi=b.ReactCurrentOwner,yi=!1;function vi(e,t,n,r){t.child=null===e?wl(t,null,n,r):bl(t,e.child,n,r)}function bi(e,t,n,r,a){n=n.render;var l=t.ref;return Rl(t,a),r=hs(e,t,n,r,l,a),n=xs(),null===e||yi?(al&&n&&el(t),t.flags|=1,vi(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Vi(e,t,a))}function wi(e,t,n,r,a){if(null===e){var l=n.type;return"function"!==typeof l||Lc(l)||void 0!==l.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Fc(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,ji(e,t,l,r,a))}if(l=e.child,0===(e.lanes&a)){var s=l.memoizedProps;if((n=null!==(n=n.compare)?n:or)(s,r)&&e.ref===t.ref)return Vi(e,t,a)}return t.flags|=1,(e=Oc(l,r)).ref=t.ref,e.return=t,t.child=e}function ji(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(or(l,r)&&e.ref===t.ref){if(yi=!1,t.pendingProps=r=l,0===(e.lanes&a))return t.lanes=e.lanes,Vi(e,t,a);0!==(131072&e.flags)&&(yi=!0)}}return Si(e,t,n,r,a)}function Ni(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ea(Oo,Lo),Lo|=n;else{if(0===(1073741824&n))return e=null!==l?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ea(Oo,Lo),Lo|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==l?l.baseLanes:n,Ea(Oo,Lo),Lo|=r}else null!==l?(r=l.baseLanes|n,t.memoizedState=null):r=n,Ea(Oo,Lo),Lo|=r;return vi(e,t,a,n),t.child}function ki(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Si(e,t,n,r,a){var l=La(n)?Pa:_a.current;return l=Ta(t,l),Rl(t,a),n=hs(e,t,n,r,l,a),r=xs(),null===e||yi?(al&&r&&el(t),t.flags|=1,vi(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Vi(e,t,a))}function Ei(e,t,n,r,a){if(La(n)){var l=!0;Ma(t)}else l=!1;if(Rl(t,a),null===t.stateNode)$i(e,t),li(t,n,r),ii(t,n,r,a),r=!0;else if(null===e){var s=t.stateNode,i=t.memoizedProps;s.props=i;var o=s.context,c=n.contextType;"object"===typeof c&&null!==c?c=Pl(c):c=Ta(t,c=La(n)?Pa:_a.current);var u=n.getDerivedStateFromProps,d="function"===typeof u||"function"===typeof s.getSnapshotBeforeUpdate;d||"function"!==typeof s.UNSAFE_componentWillReceiveProps&&"function"!==typeof s.componentWillReceiveProps||(i!==r||o!==c)&&si(t,s,r,c),zl=!1;var f=t.memoizedState;s.state=f,$l(t,r,s,a),o=t.memoizedState,i!==r||f!==o||Ra.current||zl?("function"===typeof u&&(ni(t,n,u,r),o=t.memoizedState),(i=zl||ai(t,n,i,r,f,o,c))?(d||"function"!==typeof s.UNSAFE_componentWillMount&&"function"!==typeof s.componentWillMount||("function"===typeof s.componentWillMount&&s.componentWillMount(),"function"===typeof s.UNSAFE_componentWillMount&&s.UNSAFE_componentWillMount()),"function"===typeof s.componentDidMount&&(t.flags|=4194308)):("function"===typeof s.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=o),s.props=r,s.state=o,s.context=c,r=i):("function"===typeof s.componentDidMount&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Dl(e,t),i=t.memoizedProps,c=t.type===t.elementType?i:ti(t.type,i),s.props=c,d=t.pendingProps,f=s.context,"object"===typeof(o=n.contextType)&&null!==o?o=Pl(o):o=Ta(t,o=La(n)?Pa:_a.current);var p=n.getDerivedStateFromProps;(u="function"===typeof p||"function"===typeof s.getSnapshotBeforeUpdate)||"function"!==typeof s.UNSAFE_componentWillReceiveProps&&"function"!==typeof s.componentWillReceiveProps||(i!==d||f!==o)&&si(t,s,r,o),zl=!1,f=t.memoizedState,s.state=f,$l(t,r,s,a);var m=t.memoizedState;i!==d||f!==m||Ra.current||zl?("function"===typeof p&&(ni(t,n,p,r),m=t.memoizedState),(c=zl||ai(t,n,c,r,f,m,o)||!1)?(u||"function"!==typeof s.UNSAFE_componentWillUpdate&&"function"!==typeof s.componentWillUpdate||("function"===typeof s.componentWillUpdate&&s.componentWillUpdate(r,m,o),"function"===typeof s.UNSAFE_componentWillUpdate&&s.UNSAFE_componentWillUpdate(r,m,o)),"function"===typeof s.componentDidUpdate&&(t.flags|=4),"function"===typeof s.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof s.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof s.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=m),s.props=r,s.state=m,s.context=o,r=c):("function"!==typeof s.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof s.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Ci(e,t,n,r,l,a)}function Ci(e,t,n,r,a,l){ki(e,t);var s=0!==(128&t.flags);if(!r&&!s)return a&&Da(t,n,!1),Vi(e,t,l);r=t.stateNode,gi.current=t;var i=s&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&s?(t.child=bl(t,e.child,null,l),t.child=bl(t,null,i,l)):vi(e,t,i,l),t.memoizedState=r.state,a&&Da(t,n,!0),t.child}function _i(e){var t=e.stateNode;t.pendingContext?Fa(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Fa(0,t.context,!1),Kl(e,t.containerInfo)}function Ri(e,t,n,r,a){return pl(),ml(a),t.flags|=256,vi(e,t,n,r),t.child}var Pi,Ti,Li,Oi,Fi={dehydrated:null,treeContext:null,retryLane:0};function zi(e){return{baseLanes:e,cachePool:null,transitions:null}}function Mi(e,t,n){var r,a=t.pendingProps,s=Zl.current,i=!1,o=0!==(128&t.flags);if((r=o)||(r=(null===e||null!==e.memoizedState)&&0!==(2&s)),r?(i=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(s|=1),Ea(Zl,1&s),null===e)return cl(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(o=a.children,e=a.fallback,i?(a=t.mode,i=t.child,o={mode:"hidden",children:o},0===(1&a)&&null!==i?(i.childLanes=0,i.pendingProps=o):i=Mc(o,a,0,null),e=zc(e,a,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=zi(n),t.memoizedState=Fi,e):Di(t,o));if(null!==(s=e.memoizedState)&&null!==(r=s.dehydrated))return function(e,t,n,r,a,s,i){if(n)return 256&t.flags?(t.flags&=-257,Ai(e,t,i,r=ci(Error(l(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(s=r.fallback,a=t.mode,r=Mc({mode:"visible",children:r.children},a,0,null),(s=zc(s,a,i,null)).flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,0!==(1&t.mode)&&bl(t,e.child,null,i),t.child.memoizedState=zi(i),t.memoizedState=Fi,s);if(0===(1&t.mode))return Ai(e,t,i,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var o=r.dgst;return r=o,Ai(e,t,i,r=ci(s=Error(l(419)),r,void 0))}if(o=0!==(i&e.childLanes),yi||o){if(null!==(r=Ro)){switch(i&-i){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|i))?0:a)&&a!==s.retryLane&&(s.retryLane=a,Fl(e,a),nc(r,e,a,-1))}return hc(),Ai(e,t,i,r=ci(Error(l(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Cc.bind(null,e),a._reactRetry=t,null):(e=s.treeContext,rl=ca(a.nextSibling),nl=t,al=!0,ll=null,null!==e&&(Qa[Ja++]=Ga,Qa[Ja++]=Xa,Qa[Ja++]=Ka,Ga=e.id,Xa=e.overflow,Ka=t),t=Di(t,r.children),t.flags|=4096,t)}(e,t,o,a,r,s,n);if(i){i=a.fallback,o=t.mode,r=(s=e.child).sibling;var c={mode:"hidden",children:a.children};return 0===(1&o)&&t.child!==s?((a=t.child).childLanes=0,a.pendingProps=c,t.deletions=null):(a=Oc(s,c)).subtreeFlags=14680064&s.subtreeFlags,null!==r?i=Oc(r,i):(i=zc(i,o,n,null)).flags|=2,i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,o=null===(o=e.child.memoizedState)?zi(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=Fi,a}return e=(i=e.child).sibling,a=Oc(i,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Di(e,t){return(t=Mc({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Ai(e,t,n,r){return null!==r&&ml(r),bl(t,e.child,null,n),(e=Di(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Ui(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),_l(e.return,t,n)}function Ii(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function Bi(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(vi(e,t,r.children,n),0!==(2&(r=Zl.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Ui(e,n,t);else if(19===e.tag)Ui(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ea(Zl,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===es(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Ii(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===es(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Ii(t,!0,n,null,l);break;case"together":Ii(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function $i(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Vi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Mo|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(l(153));if(null!==t.child){for(n=Oc(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Oc(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Hi(e,t){if(!al)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function qi(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Wi(e,t,n){var r=t.pendingProps;switch(tl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qi(t),null;case 1:case 17:return La(t.type)&&Oa(),qi(t),null;case 3:return r=t.stateNode,Gl(),Sa(Ra),Sa(_a),ns(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(dl(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ll&&(sc(ll),ll=null))),Ti(e,t),qi(t),null;case 5:Yl(t);var a=Jl(Ql.current);if(n=t.type,null!==e&&null!=t.stateNode)Li(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(l(166));return qi(t),null}if(e=Jl(ql.current),dl(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[fa]=t,r[pa]=s,e=0!==(1&t.mode),n){case"dialog":Ur("cancel",r),Ur("close",r);break;case"iframe":case"object":case"embed":Ur("load",r);break;case"video":case"audio":for(a=0;a<zr.length;a++)Ur(zr[a],r);break;case"source":Ur("error",r);break;case"img":case"image":case"link":Ur("error",r),Ur("load",r);break;case"details":Ur("toggle",r);break;case"input":G(r,s),Ur("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},Ur("invalid",r);break;case"textarea":ae(r,s),Ur("invalid",r)}for(var o in ye(n,s),a=null,s)if(s.hasOwnProperty(o)){var c=s[o];"children"===o?"string"===typeof c?r.textContent!==c&&(!0!==s.suppressHydrationWarning&&Yr(r.textContent,c,e),a=["children",c]):"number"===typeof c&&r.textContent!==""+c&&(!0!==s.suppressHydrationWarning&&Yr(r.textContent,c,e),a=["children",""+c]):i.hasOwnProperty(o)&&null!=c&&"onScroll"===o&&Ur("scroll",r)}switch(n){case"input":W(r),Z(r,s,!0);break;case"textarea":W(r),se(r);break;case"select":case"option":break;default:"function"===typeof s.onClick&&(r.onclick=Zr)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{o=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=ie(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=o.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),"select"===n&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[fa]=t,e[pa]=r,Pi(e,t,!1,!1),t.stateNode=e;e:{switch(o=ve(n,r),n){case"dialog":Ur("cancel",e),Ur("close",e),a=r;break;case"iframe":case"object":case"embed":Ur("load",e),a=r;break;case"video":case"audio":for(a=0;a<zr.length;a++)Ur(zr[a],e);a=r;break;case"source":Ur("error",e),a=r;break;case"img":case"image":case"link":Ur("error",e),Ur("load",e),a=r;break;case"details":Ur("toggle",e),a=r;break;case"input":G(e,r),a=K(e,r),Ur("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=D({},r,{value:void 0}),Ur("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Ur("invalid",e)}for(s in ye(n,a),c=a)if(c.hasOwnProperty(s)){var u=c[s];"style"===s?xe(e,u):"dangerouslySetInnerHTML"===s?null!=(u=u?u.__html:void 0)&&de(e,u):"children"===s?"string"===typeof u?("textarea"!==n||""!==u)&&fe(e,u):"number"===typeof u&&fe(e,""+u):"suppressContentEditableWarning"!==s&&"suppressHydrationWarning"!==s&&"autoFocus"!==s&&(i.hasOwnProperty(s)?null!=u&&"onScroll"===s&&Ur("scroll",e):null!=u&&v(e,s,u,o))}switch(n){case"input":W(e),Z(e,r,!1);break;case"textarea":W(e),se(e);break;case"option":null!=r.value&&e.setAttribute("value",""+H(r.value));break;case"select":e.multiple=!!r.multiple,null!=(s=r.value)?ne(e,!!r.multiple,s,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return qi(t),null;case 6:if(e&&null!=t.stateNode)Oi(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(l(166));if(n=Jl(Ql.current),Jl(ql.current),dl(t)){if(r=t.stateNode,n=t.memoizedProps,r[fa]=t,(s=r.nodeValue!==n)&&null!==(e=nl))switch(e.tag){case 3:Yr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Yr(r.nodeValue,n,0!==(1&e.mode))}s&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fa]=t,t.stateNode=r}return qi(t),null;case 13:if(Sa(Zl),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(al&&null!==rl&&0!==(1&t.mode)&&0===(128&t.flags))fl(),pl(),t.flags|=98560,s=!1;else if(s=dl(t),null!==r&&null!==r.dehydrated){if(null===e){if(!s)throw Error(l(318));if(!(s=null!==(s=t.memoizedState)?s.dehydrated:null))throw Error(l(317));s[fa]=t}else pl(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;qi(t),s=!1}else null!==ll&&(sc(ll),ll=null),s=!0;if(!s)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&Zl.current)?0===Fo&&(Fo=3):hc())),null!==t.updateQueue&&(t.flags|=4),qi(t),null);case 4:return Gl(),Ti(e,t),null===e&&$r(t.stateNode.containerInfo),qi(t),null;case 10:return Cl(t.type._context),qi(t),null;case 19:if(Sa(Zl),null===(s=t.memoizedState))return qi(t),null;if(r=0!==(128&t.flags),null===(o=s.rendering))if(r)Hi(s,!1);else{if(0!==Fo||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(o=es(e))){for(t.flags|=128,Hi(s,!1),null!==(r=o.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(s=n).flags&=14680066,null===(o=s.alternate)?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=o.childLanes,s.lanes=o.lanes,s.child=o.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=o.memoizedProps,s.memoizedState=o.memoizedState,s.updateQueue=o.updateQueue,s.type=o.type,e=o.dependencies,s.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ea(Zl,1&Zl.current|2),t.child}e=e.sibling}null!==s.tail&&Xe()>$o&&(t.flags|=128,r=!0,Hi(s,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=es(o))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Hi(s,!0),null===s.tail&&"hidden"===s.tailMode&&!o.alternate&&!al)return qi(t),null}else 2*Xe()-s.renderingStartTime>$o&&1073741824!==n&&(t.flags|=128,r=!0,Hi(s,!1),t.lanes=4194304);s.isBackwards?(o.sibling=t.child,t.child=o):(null!==(n=s.last)?n.sibling=o:t.child=o,s.last=o)}return null!==s.tail?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=Xe(),t.sibling=null,n=Zl.current,Ea(Zl,r?1&n|2:1&n),t):(qi(t),null);case 22:case 23:return dc(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Lo)&&(qi(t),6&t.subtreeFlags&&(t.flags|=8192)):qi(t),null;case 24:case 25:return null}throw Error(l(156,t.tag))}function Qi(e,t){switch(tl(t),t.tag){case 1:return La(t.type)&&Oa(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Gl(),Sa(Ra),Sa(_a),ns(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Yl(t),null;case 13:if(Sa(Zl),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(l(340));pl()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Sa(Zl),null;case 4:return Gl(),null;case 10:return Cl(t.type._context),null;case 22:case 23:return dc(),null;default:return null}}Pi=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ti=function(){},Li=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Jl(ql.current);var l,s=null;switch(n){case"input":a=K(e,a),r=K(e,r),s=[];break;case"select":a=D({},a,{value:void 0}),r=D({},r,{value:void 0}),s=[];break;case"textarea":a=re(e,a),r=re(e,r),s=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=Zr)}for(u in ye(n,r),n=null,a)if(!r.hasOwnProperty(u)&&a.hasOwnProperty(u)&&null!=a[u])if("style"===u){var o=a[u];for(l in o)o.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(i.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var c=r[u];if(o=null!=a?a[u]:void 0,r.hasOwnProperty(u)&&c!==o&&(null!=c||null!=o))if("style"===u)if(o){for(l in o)!o.hasOwnProperty(l)||c&&c.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in c)c.hasOwnProperty(l)&&o[l]!==c[l]&&(n||(n={}),n[l]=c[l])}else n||(s||(s=[]),s.push(u,n)),n=c;else"dangerouslySetInnerHTML"===u?(c=c?c.__html:void 0,o=o?o.__html:void 0,null!=c&&o!==c&&(s=s||[]).push(u,c)):"children"===u?"string"!==typeof c&&"number"!==typeof c||(s=s||[]).push(u,""+c):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(i.hasOwnProperty(u)?(null!=c&&"onScroll"===u&&Ur("scroll",e),s||o===c||(s=[])):(s=s||[]).push(u,c))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}},Oi=function(e,t,n,r){n!==r&&(t.flags|=4)};var Ji=!1,Ki=!1,Gi="function"===typeof WeakSet?WeakSet:Set,Xi=null;function Yi(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){kc(e,t,r)}else n.current=null}function Zi(e,t,n){try{n()}catch(r){kc(e,t,r)}}var eo=!1;function to(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var l=a.destroy;a.destroy=void 0,void 0!==l&&Zi(t,n,l)}a=a.next}while(a!==r)}}function no(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ro(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function ao(e){var t=e.alternate;null!==t&&(e.alternate=null,ao(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fa],delete t[pa],delete t[ha],delete t[xa],delete t[ga])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function lo(e){return 5===e.tag||3===e.tag||4===e.tag}function so(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||lo(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function io(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Zr));else if(4!==r&&null!==(e=e.child))for(io(e,t,n),e=e.sibling;null!==e;)io(e,t,n),e=e.sibling}function oo(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(oo(e,t,n),e=e.sibling;null!==e;)oo(e,t,n),e=e.sibling}var co=null,uo=!1;function fo(e,t,n){for(n=n.child;null!==n;)po(e,t,n),n=n.sibling}function po(e,t,n){if(lt&&"function"===typeof lt.onCommitFiberUnmount)try{lt.onCommitFiberUnmount(at,n)}catch(i){}switch(n.tag){case 5:Ki||Yi(n,t);case 6:var r=co,a=uo;co=null,fo(e,t,n),uo=a,null!==(co=r)&&(uo?(e=co,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):co.removeChild(n.stateNode));break;case 18:null!==co&&(uo?(e=co,n=n.stateNode,8===e.nodeType?oa(e.parentNode,n):1===e.nodeType&&oa(e,n),$t(e)):oa(co,n.stateNode));break;case 4:r=co,a=uo,co=n.stateNode.containerInfo,uo=!0,fo(e,t,n),co=r,uo=a;break;case 0:case 11:case 14:case 15:if(!Ki&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var l=a,s=l.destroy;l=l.tag,void 0!==s&&(0!==(2&l)||0!==(4&l))&&Zi(n,t,s),a=a.next}while(a!==r)}fo(e,t,n);break;case 1:if(!Ki&&(Yi(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(i){kc(n,t,i)}fo(e,t,n);break;case 21:fo(e,t,n);break;case 22:1&n.mode?(Ki=(r=Ki)||null!==n.memoizedState,fo(e,t,n),Ki=r):fo(e,t,n);break;default:fo(e,t,n)}}function mo(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Gi),t.forEach(function(t){var r=_c.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function ho(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var s=e,i=t,o=i;e:for(;null!==o;){switch(o.tag){case 5:co=o.stateNode,uo=!1;break e;case 3:case 4:co=o.stateNode.containerInfo,uo=!0;break e}o=o.return}if(null===co)throw Error(l(160));po(s,i,a),co=null,uo=!1;var c=a.alternate;null!==c&&(c.return=null),a.return=null}catch(u){kc(a,t,u)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)xo(t,e),t=t.sibling}function xo(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ho(t,e),go(e),4&r){try{to(3,e,e.return),no(3,e)}catch(x){kc(e,e.return,x)}try{to(5,e,e.return)}catch(x){kc(e,e.return,x)}}break;case 1:ho(t,e),go(e),512&r&&null!==n&&Yi(n,n.return);break;case 5:if(ho(t,e),go(e),512&r&&null!==n&&Yi(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(x){kc(e,e.return,x)}}if(4&r&&null!=(a=e.stateNode)){var s=e.memoizedProps,i=null!==n?n.memoizedProps:s,o=e.type,c=e.updateQueue;if(e.updateQueue=null,null!==c)try{"input"===o&&"radio"===s.type&&null!=s.name&&X(a,s),ve(o,i);var u=ve(o,s);for(i=0;i<c.length;i+=2){var d=c[i],f=c[i+1];"style"===d?xe(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):v(a,d,f,u)}switch(o){case"input":Y(a,s);break;case"textarea":le(a,s);break;case"select":var p=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!s.multiple;var m=s.value;null!=m?ne(a,!!s.multiple,m,!1):p!==!!s.multiple&&(null!=s.defaultValue?ne(a,!!s.multiple,s.defaultValue,!0):ne(a,!!s.multiple,s.multiple?[]:"",!1))}a[pa]=s}catch(x){kc(e,e.return,x)}}break;case 6:if(ho(t,e),go(e),4&r){if(null===e.stateNode)throw Error(l(162));a=e.stateNode,s=e.memoizedProps;try{a.nodeValue=s}catch(x){kc(e,e.return,x)}}break;case 3:if(ho(t,e),go(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{$t(t.containerInfo)}catch(x){kc(e,e.return,x)}break;case 4:default:ho(t,e),go(e);break;case 13:ho(t,e),go(e),8192&(a=e.child).flags&&(s=null!==a.memoizedState,a.stateNode.isHidden=s,!s||null!==a.alternate&&null!==a.alternate.memoizedState||(Bo=Xe())),4&r&&mo(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Ki=(u=Ki)||d,ho(t,e),Ki=u):ho(t,e),go(e),8192&r){if(u=null!==e.memoizedState,(e.stateNode.isHidden=u)&&!d&&0!==(1&e.mode))for(Xi=e,d=e.child;null!==d;){for(f=Xi=d;null!==Xi;){switch(m=(p=Xi).child,p.tag){case 0:case 11:case 14:case 15:to(4,p,p.return);break;case 1:Yi(p,p.return);var h=p.stateNode;if("function"===typeof h.componentWillUnmount){r=p,n=p.return;try{t=r,h.props=t.memoizedProps,h.state=t.memoizedState,h.componentWillUnmount()}catch(x){kc(r,n,x)}}break;case 5:Yi(p,p.return);break;case 22:if(null!==p.memoizedState){wo(f);continue}}null!==m?(m.return=p,Xi=m):wo(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,u?"function"===typeof(s=a.style).setProperty?s.setProperty("display","none","important"):s.display="none":(o=f.stateNode,i=void 0!==(c=f.memoizedProps.style)&&null!==c&&c.hasOwnProperty("display")?c.display:null,o.style.display=he("display",i))}catch(x){kc(e,e.return,x)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(x){kc(e,e.return,x)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ho(t,e),go(e),4&r&&mo(e);case 21:}}function go(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(lo(n)){var r=n;break e}n=n.return}throw Error(l(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),oo(e,so(e),a);break;case 3:case 4:var s=r.stateNode.containerInfo;io(e,so(e),s);break;default:throw Error(l(161))}}catch(i){kc(e,e.return,i)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function yo(e,t,n){Xi=e,vo(e,t,n)}function vo(e,t,n){for(var r=0!==(1&e.mode);null!==Xi;){var a=Xi,l=a.child;if(22===a.tag&&r){var s=null!==a.memoizedState||Ji;if(!s){var i=a.alternate,o=null!==i&&null!==i.memoizedState||Ki;i=Ji;var c=Ki;if(Ji=s,(Ki=o)&&!c)for(Xi=a;null!==Xi;)o=(s=Xi).child,22===s.tag&&null!==s.memoizedState?jo(a):null!==o?(o.return=s,Xi=o):jo(a);for(;null!==l;)Xi=l,vo(l,t,n),l=l.sibling;Xi=a,Ji=i,Ki=c}bo(e)}else 0!==(8772&a.subtreeFlags)&&null!==l?(l.return=a,Xi=l):bo(e)}}function bo(e){for(;null!==Xi;){var t=Xi;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Ki||no(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Ki)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:ti(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;null!==s&&Vl(t,s,r);break;case 3:var i=t.updateQueue;if(null!==i){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Vl(t,i,n)}break;case 5:var o=t.stateNode;if(null===n&&4&t.flags){n=o;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var u=t.alternate;if(null!==u){var d=u.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&$t(f)}}}break;default:throw Error(l(163))}Ki||512&t.flags&&ro(t)}catch(p){kc(t,t.return,p)}}if(t===e){Xi=null;break}if(null!==(n=t.sibling)){n.return=t.return,Xi=n;break}Xi=t.return}}function wo(e){for(;null!==Xi;){var t=Xi;if(t===e){Xi=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Xi=n;break}Xi=t.return}}function jo(e){for(;null!==Xi;){var t=Xi;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{no(4,t)}catch(o){kc(t,n,o)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(o){kc(t,a,o)}}var l=t.return;try{ro(t)}catch(o){kc(t,l,o)}break;case 5:var s=t.return;try{ro(t)}catch(o){kc(t,s,o)}}}catch(o){kc(t,t.return,o)}if(t===e){Xi=null;break}var i=t.sibling;if(null!==i){i.return=t.return,Xi=i;break}Xi=t.return}}var No,ko=Math.ceil,So=b.ReactCurrentDispatcher,Eo=b.ReactCurrentOwner,Co=b.ReactCurrentBatchConfig,_o=0,Ro=null,Po=null,To=0,Lo=0,Oo=ka(0),Fo=0,zo=null,Mo=0,Do=0,Ao=0,Uo=null,Io=null,Bo=0,$o=1/0,Vo=null,Ho=!1,qo=null,Wo=null,Qo=!1,Jo=null,Ko=0,Go=0,Xo=null,Yo=-1,Zo=0;function ec(){return 0!==(6&_o)?Xe():-1!==Yo?Yo:Yo=Xe()}function tc(e){return 0===(1&e.mode)?1:0!==(2&_o)&&0!==To?To&-To:null!==hl.transition?(0===Zo&&(Zo=ht()),Zo):0!==(e=vt)?e:e=void 0===(e=window.event)?16:Gt(e.type)}function nc(e,t,n,r){if(50<Go)throw Go=0,Xo=null,Error(l(185));gt(e,n,r),0!==(2&_o)&&e===Ro||(e===Ro&&(0===(2&_o)&&(Do|=n),4===Fo&&ic(e,To)),rc(e,r),1===n&&0===_o&&0===(1&t.mode)&&($o=Xe()+500,Ua&&$a()))}function rc(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=e.pendingLanes;0<l;){var s=31-st(l),i=1<<s,o=a[s];-1===o?0!==(i&n)&&0===(i&r)||(a[s]=pt(i,t)):o<=t&&(e.expiredLanes|=i),l&=~i}}(e,t);var r=ft(e,e===Ro?To:0);if(0===r)null!==n&&Je(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Je(n),1===t)0===e.tag?function(e){Ua=!0,Ba(e)}(oc.bind(null,e)):Ba(oc.bind(null,e)),sa(function(){0===(6&_o)&&$a()}),n=null;else{switch(bt(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Rc(n,ac.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ac(e,t){if(Yo=-1,Zo=0,0!==(6&_o))throw Error(l(327));var n=e.callbackNode;if(jc()&&e.callbackNode!==n)return null;var r=ft(e,e===Ro?To:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=xc(e,r);else{t=r;var a=_o;_o|=2;var s=mc();for(Ro===e&&To===t||(Vo=null,$o=Xe()+500,fc(e,t));;)try{yc();break}catch(o){pc(e,o)}El(),So.current=s,_o=a,null!==Po?t=0:(Ro=null,To=0,t=Fo)}if(0!==t){if(2===t&&(0!==(a=mt(e))&&(r=a,t=lc(e,a))),1===t)throw n=zo,fc(e,0),ic(e,r),rc(e,Xe()),n;if(6===t)ic(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!ir(l(),a))return!1}catch(i){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=xc(e,r))&&(0!==(s=mt(e))&&(r=s,t=lc(e,s))),1===t))throw n=zo,fc(e,0),ic(e,r),rc(e,Xe()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(l(345));case 2:case 5:wc(e,Io,Vo);break;case 3:if(ic(e,r),(130023424&r)===r&&10<(t=Bo+500-Xe())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){ec(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ra(wc.bind(null,e,Io,Vo),t);break}wc(e,Io,Vo);break;case 4:if(ic(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var i=31-st(r);s=1<<i,(i=t[i])>a&&(a=i),r&=~s}if(r=a,10<(r=(120>(r=Xe()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*ko(r/1960))-r)){e.timeoutHandle=ra(wc.bind(null,e,Io,Vo),r);break}wc(e,Io,Vo);break;default:throw Error(l(329))}}}return rc(e,Xe()),e.callbackNode===n?ac.bind(null,e):null}function lc(e,t){var n=Uo;return e.current.memoizedState.isDehydrated&&(fc(e,t).flags|=256),2!==(e=xc(e,t))&&(t=Io,Io=n,null!==t&&sc(t)),e}function sc(e){null===Io?Io=e:Io.push.apply(Io,e)}function ic(e,t){for(t&=~Ao,t&=~Do,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-st(t),r=1<<n;e[n]=-1,t&=~r}}function oc(e){if(0!==(6&_o))throw Error(l(327));jc();var t=ft(e,0);if(0===(1&t))return rc(e,Xe()),null;var n=xc(e,t);if(0!==e.tag&&2===n){var r=mt(e);0!==r&&(t=r,n=lc(e,r))}if(1===n)throw n=zo,fc(e,0),ic(e,t),rc(e,Xe()),n;if(6===n)throw Error(l(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wc(e,Io,Vo),rc(e,Xe()),null}function cc(e,t){var n=_o;_o|=1;try{return e(t)}finally{0===(_o=n)&&($o=Xe()+500,Ua&&$a())}}function uc(e){null!==Jo&&0===Jo.tag&&0===(6&_o)&&jc();var t=_o;_o|=1;var n=Co.transition,r=vt;try{if(Co.transition=null,vt=1,e)return e()}finally{vt=r,Co.transition=n,0===(6&(_o=t))&&$a()}}function dc(){Lo=Oo.current,Sa(Oo)}function fc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,aa(n)),null!==Po)for(n=Po.return;null!==n;){var r=n;switch(tl(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Oa();break;case 3:Gl(),Sa(Ra),Sa(_a),ns();break;case 5:Yl(r);break;case 4:Gl();break;case 13:case 19:Sa(Zl);break;case 10:Cl(r.type._context);break;case 22:case 23:dc()}n=n.return}if(Ro=e,Po=e=Oc(e.current,null),To=Lo=t,Fo=0,zo=null,Ao=Do=Mo=0,Io=Uo=null,null!==Tl){for(t=0;t<Tl.length;t++)if(null!==(r=(n=Tl[t]).interleaved)){n.interleaved=null;var a=r.next,l=n.pending;if(null!==l){var s=l.next;l.next=a,r.next=s}n.pending=r}Tl=null}return e}function pc(e,t){for(;;){var n=Po;try{if(El(),rs.current=Xs,cs){for(var r=ss.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}cs=!1}if(ls=0,os=is=ss=null,us=!1,ds=0,Eo.current=null,null===n||null===n.return){Fo=1,zo=t,Po=null;break}e:{var s=e,i=n.return,o=n,c=t;if(t=To,o.flags|=32768,null!==c&&"object"===typeof c&&"function"===typeof c.then){var u=c,d=o,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var m=hi(i);if(null!==m){m.flags&=-257,xi(m,i,o,0,t),1&m.mode&&mi(s,u,t),c=u;var h=(t=m).updateQueue;if(null===h){var x=new Set;x.add(c),t.updateQueue=x}else h.add(c);break e}if(0===(1&t)){mi(s,u,t),hc();break e}c=Error(l(426))}else if(al&&1&o.mode){var g=hi(i);if(null!==g){0===(65536&g.flags)&&(g.flags|=256),xi(g,i,o,0,t),ml(oi(c,o));break e}}s=c=oi(c,o),4!==Fo&&(Fo=2),null===Uo?Uo=[s]:Uo.push(s),s=i;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t,Bl(s,fi(0,c,t));break e;case 1:o=c;var y=s.type,v=s.stateNode;if(0===(128&s.flags)&&("function"===typeof y.getDerivedStateFromError||null!==v&&"function"===typeof v.componentDidCatch&&(null===Wo||!Wo.has(v)))){s.flags|=65536,t&=-t,s.lanes|=t,Bl(s,pi(s,o,t));break e}}s=s.return}while(null!==s)}bc(n)}catch(b){t=b,Po===n&&null!==n&&(Po=n=n.return);continue}break}}function mc(){var e=So.current;return So.current=Xs,null===e?Xs:e}function hc(){0!==Fo&&3!==Fo&&2!==Fo||(Fo=4),null===Ro||0===(268435455&Mo)&&0===(268435455&Do)||ic(Ro,To)}function xc(e,t){var n=_o;_o|=2;var r=mc();for(Ro===e&&To===t||(Vo=null,fc(e,t));;)try{gc();break}catch(a){pc(e,a)}if(El(),_o=n,So.current=r,null!==Po)throw Error(l(261));return Ro=null,To=0,Fo}function gc(){for(;null!==Po;)vc(Po)}function yc(){for(;null!==Po&&!Ke();)vc(Po)}function vc(e){var t=No(e.alternate,e,Lo);e.memoizedProps=e.pendingProps,null===t?bc(e):Po=t,Eo.current=null}function bc(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Wi(n,t,Lo)))return void(Po=n)}else{if(null!==(n=Qi(n,t)))return n.flags&=32767,void(Po=n);if(null===e)return Fo=6,void(Po=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Po=t);Po=t=e}while(null!==t);0===Fo&&(Fo=5)}function wc(e,t,n){var r=vt,a=Co.transition;try{Co.transition=null,vt=1,function(e,t,n,r){do{jc()}while(null!==Jo);if(0!==(6&_o))throw Error(l(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(l(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-st(n),l=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~l}}(e,s),e===Ro&&(Po=Ro=null,To=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Qo||(Qo=!0,Rc(tt,function(){return jc(),null})),s=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||s){s=Co.transition,Co.transition=null;var i=vt;vt=1;var o=_o;_o|=4,Eo.current=null,function(e,t){if(ea=Ht,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch(w){n=null;break e}var i=0,o=-1,c=-1,u=0,d=0,f=e,p=null;t:for(;;){for(var m;f!==n||0!==a&&3!==f.nodeType||(o=i+a),f!==s||0!==r&&3!==f.nodeType||(c=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(m=f.firstChild);)p=f,f=m;for(;;){if(f===e)break t;if(p===n&&++u===a&&(o=i),p===s&&++d===r&&(c=i),null!==(m=f.nextSibling))break;p=(f=p).parentNode}f=m}n=-1===o||-1===c?null:{start:o,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},Ht=!1,Xi=t;null!==Xi;)if(e=(t=Xi).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Xi=e;else for(;null!==Xi;){t=Xi;try{var h=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==h){var x=h.memoizedProps,g=h.memoizedState,y=t.stateNode,v=y.getSnapshotBeforeUpdate(t.elementType===t.type?x:ti(t.type,x),g);y.__reactInternalSnapshotBeforeUpdate=v}break;case 3:var b=t.stateNode.containerInfo;1===b.nodeType?b.textContent="":9===b.nodeType&&b.documentElement&&b.removeChild(b.documentElement);break;default:throw Error(l(163))}}catch(w){kc(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,Xi=e;break}Xi=t.return}h=eo,eo=!1}(e,n),xo(n,e),mr(ta),Ht=!!ea,ta=ea=null,e.current=n,yo(n,e,a),Ge(),_o=o,vt=i,Co.transition=s}else e.current=n;if(Qo&&(Qo=!1,Jo=e,Ko=a),s=e.pendingLanes,0===s&&(Wo=null),function(e){if(lt&&"function"===typeof lt.onCommitFiberRoot)try{lt.onCommitFiberRoot(at,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),rc(e,Xe()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Ho)throw Ho=!1,e=qo,qo=null,e;0!==(1&Ko)&&0!==e.tag&&jc(),s=e.pendingLanes,0!==(1&s)?e===Xo?Go++:(Go=0,Xo=e):Go=0,$a()}(e,t,n,r)}finally{Co.transition=a,vt=r}return null}function jc(){if(null!==Jo){var e=bt(Ko),t=Co.transition,n=vt;try{if(Co.transition=null,vt=16>e?16:e,null===Jo)var r=!1;else{if(e=Jo,Jo=null,Ko=0,0!==(6&_o))throw Error(l(331));var a=_o;for(_o|=4,Xi=e.current;null!==Xi;){var s=Xi,i=s.child;if(0!==(16&Xi.flags)){var o=s.deletions;if(null!==o){for(var c=0;c<o.length;c++){var u=o[c];for(Xi=u;null!==Xi;){var d=Xi;switch(d.tag){case 0:case 11:case 15:to(8,d,s)}var f=d.child;if(null!==f)f.return=d,Xi=f;else for(;null!==Xi;){var p=(d=Xi).sibling,m=d.return;if(ao(d),d===u){Xi=null;break}if(null!==p){p.return=m,Xi=p;break}Xi=m}}}var h=s.alternate;if(null!==h){var x=h.child;if(null!==x){h.child=null;do{var g=x.sibling;x.sibling=null,x=g}while(null!==x)}}Xi=s}}if(0!==(2064&s.subtreeFlags)&&null!==i)i.return=s,Xi=i;else e:for(;null!==Xi;){if(0!==(2048&(s=Xi).flags))switch(s.tag){case 0:case 11:case 15:to(9,s,s.return)}var y=s.sibling;if(null!==y){y.return=s.return,Xi=y;break e}Xi=s.return}}var v=e.current;for(Xi=v;null!==Xi;){var b=(i=Xi).child;if(0!==(2064&i.subtreeFlags)&&null!==b)b.return=i,Xi=b;else e:for(i=v;null!==Xi;){if(0!==(2048&(o=Xi).flags))try{switch(o.tag){case 0:case 11:case 15:no(9,o)}}catch(j){kc(o,o.return,j)}if(o===i){Xi=null;break e}var w=o.sibling;if(null!==w){w.return=o.return,Xi=w;break e}Xi=o.return}}if(_o=a,$a(),lt&&"function"===typeof lt.onPostCommitFiberRoot)try{lt.onPostCommitFiberRoot(at,e)}catch(j){}r=!0}return r}finally{vt=n,Co.transition=t}}return!1}function Nc(e,t,n){e=Ul(e,t=fi(0,t=oi(n,t),1),1),t=ec(),null!==e&&(gt(e,1,t),rc(e,t))}function kc(e,t,n){if(3===e.tag)Nc(e,e,n);else for(;null!==t;){if(3===t.tag){Nc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Wo||!Wo.has(r))){t=Ul(t,e=pi(t,e=oi(n,e),1),1),e=ec(),null!==t&&(gt(t,1,e),rc(t,e));break}}t=t.return}}function Sc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=ec(),e.pingedLanes|=e.suspendedLanes&n,Ro===e&&(To&n)===n&&(4===Fo||3===Fo&&(130023424&To)===To&&500>Xe()-Bo?fc(e,0):Ao|=n),rc(e,t)}function Ec(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ut,0===(130023424&(ut<<=1))&&(ut=4194304)));var n=ec();null!==(e=Fl(e,t))&&(gt(e,t,n),rc(e,n))}function Cc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Ec(e,n)}function _c(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(l(314))}null!==r&&r.delete(t),Ec(e,n)}function Rc(e,t){return Qe(e,t)}function Pc(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Tc(e,t,n,r){return new Pc(e,t,n,r)}function Lc(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Oc(e,t){var n=e.alternate;return null===n?((n=Tc(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Fc(e,t,n,r,a,s){var i=2;if(r=e,"function"===typeof e)Lc(e)&&(i=1);else if("string"===typeof e)i=5;else e:switch(e){case N:return zc(n.children,a,s,t);case k:i=8,a|=8;break;case S:return(e=Tc(12,n,t,2|a)).elementType=S,e.lanes=s,e;case R:return(e=Tc(13,n,t,a)).elementType=R,e.lanes=s,e;case P:return(e=Tc(19,n,t,a)).elementType=P,e.lanes=s,e;case O:return Mc(n,a,s,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case E:i=10;break e;case C:i=9;break e;case _:i=11;break e;case T:i=14;break e;case L:i=16,r=null;break e}throw Error(l(130,null==e?e:typeof e,""))}return(t=Tc(i,n,t,a)).elementType=e,t.type=r,t.lanes=s,t}function zc(e,t,n,r){return(e=Tc(7,e,r,t)).lanes=n,e}function Mc(e,t,n,r){return(e=Tc(22,e,r,t)).elementType=O,e.lanes=n,e.stateNode={isHidden:!1},e}function Dc(e,t,n){return(e=Tc(6,e,null,t)).lanes=n,e}function Ac(e,t,n){return(t=Tc(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Uc(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=xt(0),this.expirationTimes=xt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=xt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Ic(e,t,n,r,a,l,s,i,o){return e=new Uc(e,t,n,i,o),1===t?(t=1,!0===l&&(t|=8)):t=0,l=Tc(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ml(l),e}function Bc(e){if(!e)return Ca;e:{if($e(e=e._reactInternals)!==e||1!==e.tag)throw Error(l(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(La(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(l(171))}if(1===e.tag){var n=e.type;if(La(n))return za(e,n,t)}return t}function $c(e,t,n,r,a,l,s,i,o){return(e=Ic(n,r,!0,e,0,l,0,i,o)).context=Bc(null),n=e.current,(l=Al(r=ec(),a=tc(n))).callback=void 0!==t&&null!==t?t:null,Ul(n,l,a),e.current.lanes=a,gt(e,a,r),rc(e,r),e}function Vc(e,t,n,r){var a=t.current,l=ec(),s=tc(a);return n=Bc(n),null===t.context?t.context=n:t.pendingContext=n,(t=Al(l,s)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Ul(a,t,s))&&(nc(e,a,s,l),Il(e,a,s)),s}function Hc(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function qc(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Wc(e,t){qc(e,t),(e=e.alternate)&&qc(e,t)}No=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ra.current)yi=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return yi=!1,function(e,t,n){switch(t.tag){case 3:_i(t),pl();break;case 5:Xl(t);break;case 1:La(t.type)&&Ma(t);break;case 4:Kl(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Ea(jl,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Ea(Zl,1&Zl.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Mi(e,t,n):(Ea(Zl,1&Zl.current),null!==(e=Vi(e,t,n))?e.sibling:null);Ea(Zl,1&Zl.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Bi(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Ea(Zl,Zl.current),r)break;return null;case 22:case 23:return t.lanes=0,Ni(e,t,n)}return Vi(e,t,n)}(e,t,n);yi=0!==(131072&e.flags)}else yi=!1,al&&0!==(1048576&t.flags)&&Za(t,Wa,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;$i(e,t),e=t.pendingProps;var a=Ta(t,_a.current);Rl(t,n),a=hs(null,t,r,e,a,n);var s=xs();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,La(r)?(s=!0,Ma(t)):s=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Ml(t),a.updater=ri,t.stateNode=a,a._reactInternals=t,ii(t,r,e,n),t=Ci(null,t,r,!0,s,n)):(t.tag=0,al&&s&&el(t),vi(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch($i(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Lc(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===_)return 11;if(e===T)return 14}return 2}(r),e=ti(r,e),a){case 0:t=Si(null,t,r,e,n);break e;case 1:t=Ei(null,t,r,e,n);break e;case 11:t=bi(null,t,r,e,n);break e;case 14:t=wi(null,t,r,ti(r.type,e),n);break e}throw Error(l(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Si(e,t,r,a=t.elementType===r?a:ti(r,a),n);case 1:return r=t.type,a=t.pendingProps,Ei(e,t,r,a=t.elementType===r?a:ti(r,a),n);case 3:e:{if(_i(t),null===e)throw Error(l(387));r=t.pendingProps,a=(s=t.memoizedState).element,Dl(e,t),$l(t,r,null,n);var i=t.memoizedState;if(r=i.element,s.isDehydrated){if(s={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=s,t.memoizedState=s,256&t.flags){t=Ri(e,t,r,n,a=oi(Error(l(423)),t));break e}if(r!==a){t=Ri(e,t,r,n,a=oi(Error(l(424)),t));break e}for(rl=ca(t.stateNode.containerInfo.firstChild),nl=t,al=!0,ll=null,n=wl(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(pl(),r===a){t=Vi(e,t,n);break e}vi(e,t,r,n)}t=t.child}return t;case 5:return Xl(t),null===e&&cl(t),r=t.type,a=t.pendingProps,s=null!==e?e.memoizedProps:null,i=a.children,na(r,a)?i=null:null!==s&&na(r,s)&&(t.flags|=32),ki(e,t),vi(e,t,i,n),t.child;case 6:return null===e&&cl(t),null;case 13:return Mi(e,t,n);case 4:return Kl(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=bl(t,null,r,n):vi(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,bi(e,t,r,a=t.elementType===r?a:ti(r,a),n);case 7:return vi(e,t,t.pendingProps,n),t.child;case 8:case 12:return vi(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,s=t.memoizedProps,i=a.value,Ea(jl,r._currentValue),r._currentValue=i,null!==s)if(ir(s.value,i)){if(s.children===a.children&&!Ra.current){t=Vi(e,t,n);break e}}else for(null!==(s=t.child)&&(s.return=t);null!==s;){var o=s.dependencies;if(null!==o){i=s.child;for(var c=o.firstContext;null!==c;){if(c.context===r){if(1===s.tag){(c=Al(-1,n&-n)).tag=2;var u=s.updateQueue;if(null!==u){var d=(u=u.shared).pending;null===d?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}s.lanes|=n,null!==(c=s.alternate)&&(c.lanes|=n),_l(s.return,n,t),o.lanes|=n;break}c=c.next}}else if(10===s.tag)i=s.type===t.type?null:s.child;else if(18===s.tag){if(null===(i=s.return))throw Error(l(341));i.lanes|=n,null!==(o=i.alternate)&&(o.lanes|=n),_l(i,n,t),i=s.sibling}else i=s.child;if(null!==i)i.return=s;else for(i=s;null!==i;){if(i===t){i=null;break}if(null!==(s=i.sibling)){s.return=i.return,i=s;break}i=i.return}s=i}vi(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Rl(t,n),r=r(a=Pl(a)),t.flags|=1,vi(e,t,r,n),t.child;case 14:return a=ti(r=t.type,t.pendingProps),wi(e,t,r,a=ti(r.type,a),n);case 15:return ji(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:ti(r,a),$i(e,t),t.tag=1,La(r)?(e=!0,Ma(t)):e=!1,Rl(t,n),li(t,r,a),ii(t,r,a,n),Ci(null,t,r,!0,e,n);case 19:return Bi(e,t,n);case 22:return Ni(e,t,n)}throw Error(l(156,t.tag))};var Qc="function"===typeof reportError?reportError:function(e){console.error(e)};function Jc(e){this._internalRoot=e}function Kc(e){this._internalRoot=e}function Gc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Xc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Yc(){}function Zc(e,t,n,r,a){var l=n._reactRootContainer;if(l){var s=l;if("function"===typeof a){var i=a;a=function(){var e=Hc(s);i.call(e)}}Vc(t,s,e,a)}else s=function(e,t,n,r,a){if(a){if("function"===typeof r){var l=r;r=function(){var e=Hc(s);l.call(e)}}var s=$c(t,r,e,0,null,!1,0,"",Yc);return e._reactRootContainer=s,e[ma]=s.current,$r(8===e.nodeType?e.parentNode:e),uc(),s}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var i=r;r=function(){var e=Hc(o);i.call(e)}}var o=Ic(e,0,!1,null,0,!1,0,"",Yc);return e._reactRootContainer=o,e[ma]=o.current,$r(8===e.nodeType?e.parentNode:e),uc(function(){Vc(t,o,n,r)}),o}(n,t,e,a,r);return Hc(s)}Kc.prototype.render=Jc.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(l(409));Vc(e,t,null,null)},Kc.prototype.unmount=Jc.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;uc(function(){Vc(null,e,null,null)}),t[ma]=null}},Kc.prototype.unstable_scheduleHydration=function(e){if(e){var t=kt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ot.length&&0!==t&&t<Ot[n].priority;n++);Ot.splice(n,0,e),0===n&&Dt(e)}},wt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(yt(t,1|n),rc(t,Xe()),0===(6&_o)&&($o=Xe()+500,$a()))}break;case 13:uc(function(){var t=Fl(e,1);if(null!==t){var n=ec();nc(t,e,1,n)}}),Wc(e,1)}},jt=function(e){if(13===e.tag){var t=Fl(e,134217728);if(null!==t)nc(t,e,134217728,ec());Wc(e,134217728)}},Nt=function(e){if(13===e.tag){var t=tc(e),n=Fl(e,t);if(null!==n)nc(n,e,t,ec());Wc(e,t)}},kt=function(){return vt},St=function(e,t){var n=vt;try{return vt=e,t()}finally{vt=n}},je=function(e,t,n){switch(t){case"input":if(Y(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=wa(r);if(!a)throw Error(l(90));Q(r),Y(r,a)}}}break;case"textarea":le(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},_e=cc,Re=uc;var eu={usingClientEntryPoint:!1,Events:[va,ba,wa,Ee,Ce,cc]},tu={findFiberByHostInstance:ya,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nu={bundleType:tu.bundleType,version:tu.version,rendererPackageName:tu.rendererPackageName,rendererConfig:tu.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:b.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=qe(e))?null:e.stateNode},findFiberByHostInstance:tu.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ru=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ru.isDisabled&&ru.supportsFiber)try{at=ru.inject(nu),lt=ru}catch(ue){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=eu,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Gc(t))throw Error(l(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:j,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Gc(e))throw Error(l(299));var n=!1,r="",a=Qc;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Ic(e,1,!1,null,0,n,0,r,a),e[ma]=t.current,$r(8===e.nodeType?e.parentNode:e),new Jc(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(l(188));throw e=Object.keys(e).join(","),Error(l(268,e))}return e=null===(e=qe(t))?null:e.stateNode},t.flushSync=function(e){return uc(e)},t.hydrate=function(e,t,n){if(!Xc(t))throw Error(l(200));return Zc(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Gc(e))throw Error(l(405));var r=null!=n&&n.hydratedSources||null,a=!1,s="",i=Qc;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(s=n.identifierPrefix),void 0!==n.onRecoverableError&&(i=n.onRecoverableError)),t=$c(t,null,e,1,null!=n?n:null,a,0,s,i),e[ma]=t.current,$r(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Kc(t)},t.render=function(e,t,n){if(!Xc(t))throw Error(l(200));return Zc(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Xc(e))throw Error(l(40));return!!e._reactRootContainer&&(uc(function(){Zc(null,null,e,!1,function(){e._reactRootContainer=null,e[ma]=null})}),!0)},t.unstable_batchedUpdates=cc,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Xc(n))throw Error(l(200));if(null==e||void 0===e._reactInternals)throw Error(l(38));return Zc(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},352:(e,t,n)=>{var r=n(119);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},414:(e,t,n)=>{e.exports=n(654)},654:(e,t,n)=>{var r=n(950),a=Symbol.for("react.element"),l=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,o={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,l={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)s.call(t,r)&&!o.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===l[r]&&(l[r]=t[r]);return{$$typeof:a,type:e,key:c,ref:u,props:l,_owner:i.current}}t.Fragment=l,t.jsx=c,t.jsxs=c},761:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<l(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,s=a>>>1;r<s;){var i=2*(r+1)-1,o=e[i],c=i+1,u=e[c];if(0>l(o,n))c<a&&0>l(u,o)?(e[r]=u,e[c]=n,r=c):(e[r]=o,e[i]=n,r=i);else{if(!(c<a&&0>l(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function l(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var s=performance;t.unstable_now=function(){return s.now()}}else{var i=Date,o=i.now();t.unstable_now=function(){return i.now()-o}}var c=[],u=[],d=1,f=null,p=3,m=!1,h=!1,x=!1,g="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,v="undefined"!==typeof setImmediate?setImmediate:null;function b(e){for(var t=r(u);null!==t;){if(null===t.callback)a(u);else{if(!(t.startTime<=e))break;a(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function w(e){if(x=!1,b(e),!h)if(null!==r(c))h=!0,O(j);else{var t=r(u);null!==t&&F(w,t.startTime-e)}}function j(e,n){h=!1,x&&(x=!1,y(E),E=-1),m=!0;var l=p;try{for(b(n),f=r(c);null!==f&&(!(f.expirationTime>n)||e&&!R());){var s=f.callback;if("function"===typeof s){f.callback=null,p=f.priorityLevel;var i=s(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof i?f.callback=i:f===r(c)&&a(c),b(n)}else a(c);f=r(c)}if(null!==f)var o=!0;else{var d=r(u);null!==d&&F(w,d.startTime-n),o=!1}return o}finally{f=null,p=l,m=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var N,k=!1,S=null,E=-1,C=5,_=-1;function R(){return!(t.unstable_now()-_<C)}function P(){if(null!==S){var e=t.unstable_now();_=e;var n=!0;try{n=S(!0,e)}finally{n?N():(k=!1,S=null)}}else k=!1}if("function"===typeof v)N=function(){v(P)};else if("undefined"!==typeof MessageChannel){var T=new MessageChannel,L=T.port2;T.port1.onmessage=P,N=function(){L.postMessage(null)}}else N=function(){g(P,0)};function O(e){S=e,k||(k=!0,N())}function F(e,n){E=g(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){h||m||(h=!0,O(j))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(c)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,l){var s=t.unstable_now();switch("object"===typeof l&&null!==l?l="number"===typeof(l=l.delay)&&0<l?s+l:s:l=s,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:l,expirationTime:i=l+i,sortIndex:-1},l>s?(e.sortIndex=l,n(u,e),null===r(c)&&e===r(u)&&(x?(y(E),E=-1):x=!0,F(w,l-s))):(e.sortIndex=i,n(c,e),h||m||(h=!0,O(j))),e},t.unstable_shouldYield=R,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},950:(e,t,n)=>{e.exports=n(49)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var l=t[r]={exports:{}};return e[r](l,l.exports,n),l.exports}(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a)return r;if("object"===typeof r&&r){if(4&a&&r.__esModule)return r;if(16&a&&"function"===typeof r.then)return r}var l=Object.create(null);n.r(l);var s={};e=e||[null,t({}),t([]),t(t)];for(var i=2&a&&r;("object"==typeof i||"function"==typeof i)&&!~e.indexOf(i);i=t(i))Object.getOwnPropertyNames(i).forEach(e=>s[e]=()=>r[e]);return s.default=()=>r,n.d(l,s),l}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};n.r(r),n.d(r,{hasBrowserEnv:()=>Zt,hasStandardBrowserEnv:()=>tn,hasStandardBrowserWebWorkerEnv:()=>nn,navigator:()=>en,origin:()=>rn});var a,l=n(950),s=n.t(l,2),i=n(352),o=n(119),c=n.t(o,2);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(this,arguments)}!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(a||(a={}));const d="popstate";function f(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function p(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function m(e,t){return{usr:e.state,key:e.key,idx:t}}function h(e,t,n,r){return void 0===n&&(n=null),u({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?g(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function x(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function g(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function y(e,t,n,r){void 0===r&&(r={});let{window:l=document.defaultView,v5Compat:s=!1}=r,i=l.history,o=a.Pop,c=null,p=g();function g(){return(i.state||{idx:null}).idx}function y(){o=a.Pop;let e=g(),t=null==e?null:e-p;p=e,c&&c({action:o,location:b.location,delta:t})}function v(e){let t="null"!==l.location.origin?l.location.origin:l.location.href,n="string"===typeof e?e:x(e);return n=n.replace(/ $/,"%20"),f(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==p&&(p=0,i.replaceState(u({},i.state,{idx:p}),""));let b={get action(){return o},get location(){return e(l,i)},listen(e){if(c)throw new Error("A history only accepts one active listener");return l.addEventListener(d,y),c=e,()=>{l.removeEventListener(d,y),c=null}},createHref:e=>t(l,e),createURL:v,encodeLocation(e){let t=v(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){o=a.Push;let r=h(b.location,e,t);n&&n(r,e),p=g()+1;let u=m(r,p),d=b.createHref(r);try{i.pushState(u,"",d)}catch(f){if(f instanceof DOMException&&"DataCloneError"===f.name)throw f;l.location.assign(d)}s&&c&&c({action:o,location:b.location,delta:1})},replace:function(e,t){o=a.Replace;let r=h(b.location,e,t);n&&n(r,e),p=g();let l=m(r,p),u=b.createHref(r);i.replaceState(l,"",u),s&&c&&c({action:o,location:b.location,delta:0})},go:e=>i.go(e)};return b}var v;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(v||(v={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function b(e,t,n){return void 0===n&&(n="/"),w(e,t,n,!1)}function w(e,t,n,r){let a=z(("string"===typeof t?g(t):t).pathname||"/",n);if(null==a)return null;let l=j(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(l);let s=null;for(let i=0;null==s&&i<l.length;++i){let e=F(a);s=L(l[i],e,r)}return s}function j(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,l)=>{let s={relativePath:void 0===l?e.path||"":l,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};s.relativePath.startsWith("/")&&(f(s.relativePath.startsWith(r),'Absolute route path "'+s.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),s.relativePath=s.relativePath.slice(r.length));let i=I([r,s.relativePath]),o=n.concat(s);e.children&&e.children.length>0&&(f(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+i+'".'),j(e.children,t,o,i)),(null!=e.path||e.index)&&t.push({path:i,score:T(i,e.index),routesMeta:o})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of N(e.path))a(e,t,r);else a(e,t)}),t}function N(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(0===r.length)return a?[l,""]:[l];let s=N(r.join("/")),i=[];return i.push(...s.map(e=>""===e?l:[l,e].join("/"))),a&&i.push(...s),i.map(t=>e.startsWith("/")&&""===t?"/":t)}const k=/^:[\w-]+$/,S=3,E=2,C=1,_=10,R=-2,P=e=>"*"===e;function T(e,t){let n=e.split("/"),r=n.length;return n.some(P)&&(r+=R),t&&(r+=E),n.filter(e=>!P(e)).reduce((e,t)=>e+(k.test(t)?S:""===t?C:_),r)}function L(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,a={},l="/",s=[];for(let i=0;i<r.length;++i){let e=r[i],o=i===r.length-1,c="/"===l?t:t.slice(l.length)||"/",u=O({path:e.relativePath,caseSensitive:e.caseSensitive,end:o},c),d=e.route;if(!u&&o&&n&&!r[r.length-1].route.index&&(u=O({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},c)),!u)return null;Object.assign(a,u.params),s.push({params:a,pathname:I([l,u.pathname]),pathnameBase:B(I([l,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(l=I([l,u.pathnameBase]))}return s}function O(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);p("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let l=new RegExp(a,t?void 0:"i");return[l,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],s=l.replace(/(.)\/+$/,"$1"),i=a.slice(1);return{params:r.reduce((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=i[n]||"";s=l.slice(0,l.length-e.length).replace(/(.)\/+$/,"$1")}const o=i[n];return e[r]=a&&!o?void 0:(o||"").replace(/%2F/g,"/"),e},{}),pathname:l,pathnameBase:s,pattern:e}}function F(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return p(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function z(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function M(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function D(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function A(e,t){let n=D(e);return t?n.map((e,t)=>t===n.length-1?e.pathname:e.pathnameBase):n.map(e=>e.pathnameBase)}function U(e,t,n,r){let a;void 0===r&&(r=!1),"string"===typeof e?a=g(e):(a=u({},e),f(!a.pathname||!a.pathname.includes("?"),M("?","pathname","search",a)),f(!a.pathname||!a.pathname.includes("#"),M("#","pathname","hash",a)),f(!a.search||!a.search.includes("#"),M("#","search","hash",a)));let l,s=""===e||""===a.pathname,i=s?"/":a.pathname;if(null==i)l=n;else{let e=t.length-1;if(!r&&i.startsWith("..")){let t=i.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}l=e>=0?t[e]:"/"}let o=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"===typeof e?g(e):e,l=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:l,search:$(r),hash:V(a)}}(a,l),c=i&&"/"!==i&&i.endsWith("/"),d=(s||"."===i)&&n.endsWith("/");return o.pathname.endsWith("/")||!c&&!d||(o.pathname+="/"),o}const I=e=>e.join("/").replace(/\/\/+/g,"/"),B=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),$=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",V=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;function H(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const q=["post","put","patch","delete"],W=(new Set(q),["get",...q]);new Set(W),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function Q(){return Q=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Q.apply(this,arguments)}const J=l.createContext(null);const K=l.createContext(null);const G=l.createContext(null);const X=l.createContext(null);const Y=l.createContext({outlet:null,matches:[],isDataRoute:!1});const Z=l.createContext(null);function ee(){return null!=l.useContext(X)}function te(){return ee()||f(!1),l.useContext(X).location}function ne(e){l.useContext(G).static||l.useLayoutEffect(e)}function re(){let{isDataRoute:e}=l.useContext(Y);return e?function(){let{router:e}=pe(de.UseNavigateStable),t=he(fe.UseNavigateStable),n=l.useRef(!1);return ne(()=>{n.current=!0}),l.useCallback(function(r,a){void 0===a&&(a={}),n.current&&("number"===typeof r?e.navigate(r):e.navigate(r,Q({fromRouteId:t},a)))},[e,t])}():function(){ee()||f(!1);let e=l.useContext(J),{basename:t,future:n,navigator:r}=l.useContext(G),{matches:a}=l.useContext(Y),{pathname:s}=te(),i=JSON.stringify(A(a,n.v7_relativeSplatPath)),o=l.useRef(!1);return ne(()=>{o.current=!0}),l.useCallback(function(n,a){if(void 0===a&&(a={}),!o.current)return;if("number"===typeof n)return void r.go(n);let l=U(n,JSON.parse(i),s,"path"===a.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:I([t,l.pathname])),(a.replace?r.replace:r.push)(l,a.state,a)},[t,r,i,s,e])}()}function ae(e,t){let{relative:n}=void 0===t?{}:t,{future:r}=l.useContext(G),{matches:a}=l.useContext(Y),{pathname:s}=te(),i=JSON.stringify(A(a,r.v7_relativeSplatPath));return l.useMemo(()=>U(e,JSON.parse(i),s,"path"===n),[e,i,s,n])}function le(e,t,n,r){ee()||f(!1);let{navigator:s}=l.useContext(G),{matches:i}=l.useContext(Y),o=i[i.length-1],c=o?o.params:{},u=(o&&o.pathname,o?o.pathnameBase:"/");o&&o.route;let d,p=te();if(t){var m;let e="string"===typeof t?g(t):t;"/"===u||(null==(m=e.pathname)?void 0:m.startsWith(u))||f(!1),d=e}else d=p;let h=d.pathname||"/",x=h;if("/"!==u){let e=u.replace(/^\//,"").split("/");x="/"+h.replace(/^\//,"").split("/").slice(e.length).join("/")}let y=b(e,{pathname:x});let v=ue(y&&y.map(e=>Object.assign({},e,{params:Object.assign({},c,e.params),pathname:I([u,s.encodeLocation?s.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?u:I([u,s.encodeLocation?s.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),i,n,r);return t&&v?l.createElement(X.Provider,{value:{location:Q({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:a.Pop}},v):v}function se(){let e=function(){var e;let t=l.useContext(Z),n=me(fe.UseRouteError),r=he(fe.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=H(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:r};return l.createElement(l.Fragment,null,l.createElement("h2",null,"Unexpected Application Error!"),l.createElement("h3",{style:{fontStyle:"italic"}},t),n?l.createElement("pre",{style:a},n):null,null)}const ie=l.createElement(se,null);class oe extends l.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?l.createElement(Y.Provider,{value:this.props.routeContext},l.createElement(Z.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ce(e){let{routeContext:t,match:n,children:r}=e,a=l.useContext(J);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),l.createElement(Y.Provider,{value:t},r)}function ue(e,t,n,r){var a;if(void 0===t&&(t=[]),void 0===n&&(n=null),void 0===r&&(r=null),null==e){var s;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(s=r)&&s.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let i=e,o=null==(a=n)?void 0:a.errors;if(null!=o){let e=i.findIndex(e=>e.route.id&&void 0!==(null==o?void 0:o[e.route.id]));e>=0||f(!1),i=i.slice(0,Math.min(i.length,e+1))}let c=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let l=0;l<i.length;l++){let e=i[l];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(u=l),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){c=!0,i=u>=0?i.slice(0,u+1):[i[0]];break}}}return i.reduceRight((e,r,a)=>{let s,d=!1,f=null,p=null;var m;n&&(s=o&&r.route.id?o[r.route.id]:void 0,f=r.route.errorElement||ie,c&&(u<0&&0===a?(m="route-fallback",!1||xe[m]||(xe[m]=!0),d=!0,p=null):u===a&&(d=!0,p=r.route.hydrateFallbackElement||null)));let h=t.concat(i.slice(0,a+1)),x=()=>{let t;return t=s?f:d?p:r.route.Component?l.createElement(r.route.Component,null):r.route.element?r.route.element:e,l.createElement(ce,{match:r,routeContext:{outlet:e,matches:h,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===a)?l.createElement(oe,{location:n.location,revalidation:n.revalidation,component:f,error:s,children:x(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):x()},null)}var de=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(de||{}),fe=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(fe||{});function pe(e){let t=l.useContext(J);return t||f(!1),t}function me(e){let t=l.useContext(K);return t||f(!1),t}function he(e){let t=function(){let e=l.useContext(Y);return e||f(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||f(!1),n.route.id}const xe={};function ge(e,t){null==e||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}s.startTransition;function ye(e){let{to:t,replace:n,state:r,relative:a}=e;ee()||f(!1);let{future:s,static:i}=l.useContext(G),{matches:o}=l.useContext(Y),{pathname:c}=te(),u=re(),d=U(t,A(o,s.v7_relativeSplatPath),c,"path"===a),p=JSON.stringify(d);return l.useEffect(()=>u(JSON.parse(p),{replace:n,state:r,relative:a}),[u,p,a,n,r]),null}function ve(e){f(!1)}function be(e){let{basename:t="/",children:n=null,location:r,navigationType:s=a.Pop,navigator:i,static:o=!1,future:c}=e;ee()&&f(!1);let u=t.replace(/^\/*/,"/"),d=l.useMemo(()=>({basename:u,navigator:i,static:o,future:Q({v7_relativeSplatPath:!1},c)}),[u,c,i,o]);"string"===typeof r&&(r=g(r));let{pathname:p="/",search:m="",hash:h="",state:x=null,key:y="default"}=r,v=l.useMemo(()=>{let e=z(p,u);return null==e?null:{location:{pathname:e,search:m,hash:h,state:x,key:y},navigationType:s}},[u,p,m,h,x,y,s]);return null==v?null:l.createElement(G.Provider,{value:d},l.createElement(X.Provider,{children:n,value:v}))}function we(e){let{children:t,location:n}=e;return le(je(t),n)}new Promise(()=>{});l.Component;function je(e,t){void 0===t&&(t=[]);let n=[];return l.Children.forEach(e,(e,r)=>{if(!l.isValidElement(e))return;let a=[...t,r];if(e.type===l.Fragment)return void n.push.apply(n,je(e.props.children,a));e.type!==ve&&f(!1),e.props.index&&e.props.children&&f(!1);let s={id:e.props.id||a.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(s.children=je(e.props.children,a)),n.push(s)}),n}function Ne(){return Ne=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ne.apply(this,arguments)}function ke(e,t){if(null==e)return{};var n,r,a={},l=Object.keys(e);for(r=0;r<l.length;r++)n=l[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);const Se=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(aa){}new Map;const Ee=s.startTransition;c.flushSync,s.useId;function Ce(e){let{basename:t,children:n,future:r,window:a}=e,s=l.useRef();var i;null==s.current&&(s.current=(void 0===(i={window:a,v5Compat:!0})&&(i={}),y(function(e,t){let{pathname:n,search:r,hash:a}=e.location;return h("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"===typeof t?t:x(t)},null,i)));let o=s.current,[c,u]=l.useState({action:o.action,location:o.location}),{v7_startTransition:d}=r||{},f=l.useCallback(e=>{d&&Ee?Ee(()=>u(e)):u(e)},[u,d]);return l.useLayoutEffect(()=>o.listen(f),[o,f]),l.useEffect(()=>ge(r),[r]),l.createElement(be,{basename:t,children:n,location:c.location,navigationType:c.action,navigator:o,future:r})}const _e="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement,Re=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Pe=l.forwardRef(function(e,t){let n,{onClick:r,relative:a,reloadDocument:s,replace:i,state:o,target:c,to:u,preventScrollReset:d,viewTransition:p}=e,m=ke(e,Se),{basename:h}=l.useContext(G),g=!1;if("string"===typeof u&&Re.test(u)&&(n=u,_e))try{let e=new URL(window.location.href),t=u.startsWith("//")?new URL(e.protocol+u):new URL(u),n=z(t.pathname,h);t.origin===e.origin&&null!=n?u=n+t.search+t.hash:g=!0}catch(aa){}let y=function(e,t){let{relative:n}=void 0===t?{}:t;ee()||f(!1);let{basename:r,navigator:a}=l.useContext(G),{hash:s,pathname:i,search:o}=ae(e,{relative:n}),c=i;return"/"!==r&&(c="/"===i?r:I([r,i])),a.createHref({pathname:c,search:o,hash:s})}(u,{relative:a}),v=function(e,t){let{target:n,replace:r,state:a,preventScrollReset:s,relative:i,viewTransition:o}=void 0===t?{}:t,c=re(),u=te(),d=ae(e,{relative:i});return l.useCallback(t=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(t,n)){t.preventDefault();let n=void 0!==r?r:x(u)===x(d);c(e,{replace:n,state:a,preventScrollReset:s,relative:i,viewTransition:o})}},[u,c,d,r,a,n,e,s,i,o])}(u,{replace:i,state:o,target:c,preventScrollReset:d,relative:a,viewTransition:p});return l.createElement("a",Ne({},m,{href:n||y,onClick:g||s?r:function(e){r&&r(e),e.defaultPrevented||v(e)},ref:t,target:c}))});var Te,Le;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Te||(Te={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(Le||(Le={}));var Oe={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Fe=(e,t)=>{const n=(0,l.forwardRef)((n,r)=>{let{color:a="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:c="",children:u,...d}=n;return(0,l.createElement)("svg",{ref:r,...Oe,width:s,height:s,stroke:a,strokeWidth:o?24*Number(i)/Number(s):i,className:["lucide",`lucide-${f=e,f.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim()}`,c].join(" "),...d},[...t.map(e=>{let[t,n]=e;return(0,l.createElement)(t,n)}),...Array.isArray(u)?u:[u]]);var f});return n.displayName=`${e}`,n},ze=Fe("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]),Me=Fe("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]),De=Fe("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),Ae=Fe("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]]),Ue=Fe("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]),Ie=Fe("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Be=Fe("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]]),$e=Fe("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]);var Ve=n(414);const He=e=>{let{children:t}=e;const n=te(),r=[{name:"Dashboard",href:"/dashboard",icon:ze},{name:"All Jobs",href:"/jobs",icon:Me},{name:"Create Job",href:"/jobs/create",icon:De},{name:"Batch Jobs",href:"/batches",icon:Ae},{name:"Files",href:"/files",icon:Ue},{name:"Hardware",href:"/hardware",icon:Ie}];return(0,Ve.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,Ve.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,Ve.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,Ve.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,Ve.jsxs)("div",{className:"flex items-center",children:[(0,Ve.jsx)(Be,{className:"h-8 w-8 text-primary-600 mr-3"}),(0,Ve.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"AI Fine-Tuning Manager"})]}),(0,Ve.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,Ve.jsx)($e,{className:"h-5 w-5 text-green-500"}),(0,Ve.jsx)("span",{className:"text-sm text-gray-600",children:"Connected"})]})]})})}),(0,Ve.jsxs)("div",{className:"flex",children:[(0,Ve.jsxs)("nav",{className:"w-64 bg-white shadow-sm min-h-screen border-r border-gray-200",children:[(0,Ve.jsx)("div",{className:"p-4",children:(0,Ve.jsx)("ul",{className:"space-y-2",children:r.map(e=>{const t=e.icon;return(0,Ve.jsx)("li",{children:(0,Ve.jsxs)(Pe,{to:e.href,className:"flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors "+(r=e.href,n.pathname===r?"bg-primary-100 text-primary-700 border-r-2 border-primary-600":"text-gray-600 hover:bg-gray-100 hover:text-gray-900"),children:[(0,Ve.jsx)(t,{className:"h-5 w-5 mr-3"}),e.name]})},e.name);var r})})}),(0,Ve.jsxs)("div",{className:"p-4 border-t border-gray-200 mt-8",children:[(0,Ve.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Quick Stats"}),(0,Ve.jsxs)("div",{className:"space-y-2",children:[(0,Ve.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,Ve.jsx)("span",{className:"text-gray-600",children:"Active Jobs"}),(0,Ve.jsx)("span",{className:"font-medium text-blue-600",children:"-"})]}),(0,Ve.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,Ve.jsx)("span",{className:"text-gray-600",children:"Completed"}),(0,Ve.jsx)("span",{className:"font-medium text-green-600",children:"-"})]}),(0,Ve.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,Ve.jsx)("span",{className:"text-gray-600",children:"Files"}),(0,Ve.jsx)("span",{className:"font-medium text-purple-600",children:"-"})]})]})]})]}),(0,Ve.jsx)("main",{className:"flex-1 p-6",children:t})]})]})},qe=Fe("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),We=Fe("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),Qe=Fe("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);function Je(e,t){return function(){return e.apply(t,arguments)}}const{toString:Ke}=Object.prototype,{getPrototypeOf:Ge}=Object,{iterator:Xe,toStringTag:Ye}=Symbol,Ze=(et=Object.create(null),e=>{const t=Ke.call(e);return et[t]||(et[t]=t.slice(8,-1).toLowerCase())});var et;const tt=e=>(e=e.toLowerCase(),t=>Ze(t)===e),nt=e=>t=>typeof t===e,{isArray:rt}=Array,at=nt("undefined");function lt(e){return null!==e&&!at(e)&&null!==e.constructor&&!at(e.constructor)&&ot(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const st=tt("ArrayBuffer");const it=nt("string"),ot=nt("function"),ct=nt("number"),ut=e=>null!==e&&"object"===typeof e,dt=e=>{if("object"!==Ze(e))return!1;const t=Ge(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Ye in e)&&!(Xe in e)},ft=tt("Date"),pt=tt("File"),mt=tt("Blob"),ht=tt("FileList"),xt=tt("URLSearchParams"),[gt,yt,vt,bt]=["ReadableStream","Request","Response","Headers"].map(tt);function wt(e,t){let n,r,{allOwnKeys:a=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),rt(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{if(lt(e))return;const r=a?Object.getOwnPropertyNames(e):Object.keys(e),l=r.length;let s;for(n=0;n<l;n++)s=r[n],t.call(null,e[s],s,e)}}function jt(e,t){if(lt(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r,a=n.length;for(;a-- >0;)if(r=n[a],t===r.toLowerCase())return r;return null}const Nt="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,kt=e=>!at(e)&&e!==Nt;const St=(Et="undefined"!==typeof Uint8Array&&Ge(Uint8Array),e=>Et&&e instanceof Et);var Et;const Ct=tt("HTMLFormElement"),_t=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),Rt=tt("RegExp"),Pt=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};wt(n,(n,a)=>{let l;!1!==(l=t(n,a,e))&&(r[a]=l||n)}),Object.defineProperties(e,r)};const Tt=tt("AsyncFunction"),Lt=((e,t)=>{return e?setImmediate:t?(n=`axios@${Math.random()}`,r=[],Nt.addEventListener("message",e=>{let{source:t,data:a}=e;t===Nt&&a===n&&r.length&&r.shift()()},!1),e=>{r.push(e),Nt.postMessage(n,"*")}):e=>setTimeout(e);var n,r})("function"===typeof setImmediate,ot(Nt.postMessage)),Ot="undefined"!==typeof queueMicrotask?queueMicrotask.bind(Nt):"undefined"!==typeof process&&process.nextTick||Lt,Ft={isArray:rt,isArrayBuffer:st,isBuffer:lt,isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||ot(e.append)&&("formdata"===(t=Ze(e))||"object"===t&&ot(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&st(e.buffer),t},isString:it,isNumber:ct,isBoolean:e=>!0===e||!1===e,isObject:ut,isPlainObject:dt,isEmptyObject:e=>{if(!ut(e)||lt(e))return!1;try{return 0===Object.keys(e).length&&Object.getPrototypeOf(e)===Object.prototype}catch(aa){return!1}},isReadableStream:gt,isRequest:yt,isResponse:vt,isHeaders:bt,isUndefined:at,isDate:ft,isFile:pt,isBlob:mt,isRegExp:Rt,isFunction:ot,isStream:e=>ut(e)&&ot(e.pipe),isURLSearchParams:xt,isTypedArray:St,isFileList:ht,forEach:wt,merge:function e(){const{caseless:t}=kt(this)&&this||{},n={},r=(r,a)=>{const l=t&&jt(n,a)||a;dt(n[l])&&dt(r)?n[l]=e(n[l],r):dt(r)?n[l]=e({},r):rt(r)?n[l]=r.slice():n[l]=r};for(let a=0,l=arguments.length;a<l;a++)arguments[a]&&wt(arguments[a],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return wt(t,(t,r)=>{n&&ot(t)?e[r]=Je(t,n):e[r]=t},{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let a,l,s;const i={};if(t=t||{},null==e)return t;do{for(a=Object.getOwnPropertyNames(e),l=a.length;l-- >0;)s=a[l],r&&!r(s,e,t)||i[s]||(t[s]=e[s],i[s]=!0);e=!1!==n&&Ge(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:Ze,kindOfTest:tt,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(rt(e))return e;let t=e.length;if(!ct(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Xe]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:Ct,hasOwnProperty:_t,hasOwnProp:_t,reduceDescriptors:Pt,freezeMethods:e=>{Pt(e,(t,n)=>{if(ot(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];ot(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach(e=>{n[e]=!0})};return rt(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:jt,global:Nt,isContextDefined:kt,isSpecCompliantForm:function(e){return!!(e&&ot(e.append)&&"FormData"===e[Ye]&&e[Xe])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(ut(e)){if(t.indexOf(e)>=0)return;if(lt(e))return e;if(!("toJSON"in e)){t[r]=e;const a=rt(e)?[]:{};return wt(e,(e,t)=>{const l=n(e,r+1);!at(l)&&(a[t]=l)}),t[r]=void 0,a}}return e};return n(e,0)},isAsyncFn:Tt,isThenable:e=>e&&(ut(e)||ot(e))&&ot(e.then)&&ot(e.catch),setImmediate:Lt,asap:Ot,isIterable:e=>null!=e&&ot(e[Xe])};function zt(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}Ft.inherits(zt,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Ft.toJSONObject(this.config),code:this.code,status:this.status}}});const Mt=zt.prototype,Dt={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Dt[e]={value:e}}),Object.defineProperties(zt,Dt),Object.defineProperty(Mt,"isAxiosError",{value:!0}),zt.from=(e,t,n,r,a,l)=>{const s=Object.create(Mt);return Ft.toFlatObject(e,s,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),zt.call(s,e.message,t,n,r,a),s.cause=e,s.name=e.name,l&&Object.assign(s,l),s};const At=zt;function Ut(e){return Ft.isPlainObject(e)||Ft.isArray(e)}function It(e){return Ft.endsWith(e,"[]")?e.slice(0,-2):e}function Bt(e,t,n){return e?e.concat(t).map(function(e,t){return e=It(e),!n&&t?"["+e+"]":e}).join(n?".":""):t}const $t=Ft.toFlatObject(Ft,{},null,function(e){return/^is[A-Z]/.test(e)});const Vt=function(e,t,n){if(!Ft.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=Ft.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!Ft.isUndefined(t[e])})).metaTokens,a=n.visitor||c,l=n.dots,s=n.indexes,i=(n.Blob||"undefined"!==typeof Blob&&Blob)&&Ft.isSpecCompliantForm(t);if(!Ft.isFunction(a))throw new TypeError("visitor must be a function");function o(e){if(null===e)return"";if(Ft.isDate(e))return e.toISOString();if(Ft.isBoolean(e))return e.toString();if(!i&&Ft.isBlob(e))throw new At("Blob is not supported. Use a Buffer instead.");return Ft.isArrayBuffer(e)||Ft.isTypedArray(e)?i&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function c(e,n,a){let i=e;if(e&&!a&&"object"===typeof e)if(Ft.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(Ft.isArray(e)&&function(e){return Ft.isArray(e)&&!e.some(Ut)}(e)||(Ft.isFileList(e)||Ft.endsWith(n,"[]"))&&(i=Ft.toArray(e)))return n=It(n),i.forEach(function(e,r){!Ft.isUndefined(e)&&null!==e&&t.append(!0===s?Bt([n],r,l):null===s?n:n+"[]",o(e))}),!1;return!!Ut(e)||(t.append(Bt(a,n,l),o(e)),!1)}const u=[],d=Object.assign($t,{defaultVisitor:c,convertValue:o,isVisitable:Ut});if(!Ft.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!Ft.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+r.join("."));u.push(n),Ft.forEach(n,function(n,l){!0===(!(Ft.isUndefined(n)||null===n)&&a.call(t,n,Ft.isString(l)?l.trim():l,r,d))&&e(n,r?r.concat(l):[l])}),u.pop()}}(e),t};function Ht(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function qt(e,t){this._pairs=[],e&&Vt(e,this,t)}const Wt=qt.prototype;Wt.append=function(e,t){this._pairs.push([e,t])},Wt.toString=function(e){const t=e?function(t){return e.call(this,t,Ht)}:Ht;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};const Qt=qt;function Jt(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Kt(e,t,n){if(!t)return e;const r=n&&n.encode||Jt;Ft.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let l;if(l=a?a(t,n):Ft.isURLSearchParams(t)?t.toString():new Qt(t,n).toString(r),l){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+l}return e}const Gt=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){Ft.forEach(this.handlers,function(t){null!==t&&e(t)})}},Xt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Yt={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:Qt,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Zt="undefined"!==typeof window&&"undefined"!==typeof document,en="object"===typeof navigator&&navigator||void 0,tn=Zt&&(!en||["ReactNative","NativeScript","NS"].indexOf(en.product)<0),nn="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,rn=Zt&&window.location.href||"http://localhost",an={...r,...Yt};const ln=function(e){function t(e,n,r,a){let l=e[a++];if("__proto__"===l)return!0;const s=Number.isFinite(+l),i=a>=e.length;if(l=!l&&Ft.isArray(r)?r.length:l,i)return Ft.hasOwnProp(r,l)?r[l]=[r[l],n]:r[l]=n,!s;r[l]&&Ft.isObject(r[l])||(r[l]=[]);return t(e,n,r[l],a)&&Ft.isArray(r[l])&&(r[l]=function(e){const t={},n=Object.keys(e);let r;const a=n.length;let l;for(r=0;r<a;r++)l=n[r],t[l]=e[l];return t}(r[l])),!s}if(Ft.isFormData(e)&&Ft.isFunction(e.entries)){const n={};return Ft.forEachEntry(e,(e,r)=>{t(function(e){return Ft.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0])}(e),r,n,0)}),n}return null};const sn={transitional:Xt,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,a=Ft.isObject(e);a&&Ft.isHTMLForm(e)&&(e=new FormData(e));if(Ft.isFormData(e))return r?JSON.stringify(ln(e)):e;if(Ft.isArrayBuffer(e)||Ft.isBuffer(e)||Ft.isStream(e)||Ft.isFile(e)||Ft.isBlob(e)||Ft.isReadableStream(e))return e;if(Ft.isArrayBufferView(e))return e.buffer;if(Ft.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let l;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return Vt(e,new an.classes.URLSearchParams,{visitor:function(e,t,n,r){return an.isNode&&Ft.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)},...t})}(e,this.formSerializer).toString();if((l=Ft.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return Vt(l?{"files[]":e}:e,t&&new t,this.formSerializer)}}return a||r?(t.setContentType("application/json",!1),function(e,t,n){if(Ft.isString(e))try{return(t||JSON.parse)(e),Ft.trim(e)}catch(aa){if("SyntaxError"!==aa.name)throw aa}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||sn.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(Ft.isResponse(e)||Ft.isReadableStream(e))return e;if(e&&Ft.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(aa){if(n){if("SyntaxError"===aa.name)throw At.from(aa,At.ERR_BAD_RESPONSE,this,null,this.response);throw aa}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:an.classes.FormData,Blob:an.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};Ft.forEach(["delete","get","head","post","put","patch"],e=>{sn.headers[e]={}});const on=sn,cn=Ft.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),un=Symbol("internals");function dn(e){return e&&String(e).trim().toLowerCase()}function fn(e){return!1===e||null==e?e:Ft.isArray(e)?e.map(fn):String(e)}function pn(e,t,n,r,a){return Ft.isFunction(r)?r.call(this,t,n):(a&&(t=n),Ft.isString(t)?Ft.isString(r)?-1!==t.indexOf(r):Ft.isRegExp(r)?r.test(t):void 0:void 0)}class mn{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function a(e,t,n){const a=dn(t);if(!a)throw new Error("header name must be a non-empty string");const l=Ft.findKey(r,a);(!l||void 0===r[l]||!0===n||void 0===n&&!1!==r[l])&&(r[l||t]=fn(e))}const l=(e,t)=>Ft.forEach(e,(e,n)=>a(e,n,t));if(Ft.isPlainObject(e)||e instanceof this.constructor)l(e,t);else if(Ft.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))l((e=>{const t={};let n,r,a;return e&&e.split("\n").forEach(function(e){a=e.indexOf(":"),n=e.substring(0,a).trim().toLowerCase(),r=e.substring(a+1).trim(),!n||t[n]&&cn[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t})(e),t);else if(Ft.isObject(e)&&Ft.isIterable(e)){let n,r,a={};for(const t of e){if(!Ft.isArray(t))throw TypeError("Object iterator must return a key-value pair");a[r=t[0]]=(n=a[r])?Ft.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}l(a,t)}else null!=e&&a(t,e,n);return this}get(e,t){if(e=dn(e)){const n=Ft.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(Ft.isFunction(t))return t.call(this,e,n);if(Ft.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=dn(e)){const n=Ft.findKey(this,e);return!(!n||void 0===this[n]||t&&!pn(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function a(e){if(e=dn(e)){const a=Ft.findKey(n,e);!a||t&&!pn(0,n[a],a,t)||(delete n[a],r=!0)}}return Ft.isArray(e)?e.forEach(a):a(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const a=t[n];e&&!pn(0,this[a],a,e,!0)||(delete this[a],r=!0)}return r}normalize(e){const t=this,n={};return Ft.forEach(this,(r,a)=>{const l=Ft.findKey(n,a);if(l)return t[l]=fn(r),void delete t[a];const s=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}(a):String(a).trim();s!==a&&delete t[a],t[s]=fn(r),n[s]=!0}),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return Ft.forEach(this,(n,r)=>{null!=n&&!1!==n&&(t[r]=e&&Ft.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(e=>{let[t,n]=e;return t+": "+n}).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.forEach(e=>t.set(e)),t}static accessor(e){const t=(this[un]=this[un]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=dn(e);t[r]||(!function(e,t){const n=Ft.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(e,n,a){return this[r].call(this,t,e,n,a)},configurable:!0})})}(n,e),t[r]=!0)}return Ft.isArray(e)?e.forEach(r):r(e),this}}mn.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),Ft.reduceDescriptors(mn.prototype,(e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}}),Ft.freezeMethods(mn);const hn=mn;function xn(e,t){const n=this||on,r=t||n,a=hn.from(r.headers);let l=r.data;return Ft.forEach(e,function(e){l=e.call(n,l,a.normalize(),t?t.status:void 0)}),a.normalize(),l}function gn(e){return!(!e||!e.__CANCEL__)}function yn(e,t,n){At.call(this,null==e?"canceled":e,At.ERR_CANCELED,t,n),this.name="CanceledError"}Ft.inherits(yn,At,{__CANCEL__:!0});const vn=yn;function bn(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new At("Request failed with status code "+n.status,[At.ERR_BAD_REQUEST,At.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const wn=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a,l=0,s=0;return t=void 0!==t?t:1e3,function(i){const o=Date.now(),c=r[s];a||(a=o),n[l]=i,r[l]=o;let u=s,d=0;for(;u!==l;)d+=n[u++],u%=e;if(l=(l+1)%e,l===s&&(s=(s+1)%e),o-a<t)return;const f=c&&o-c;return f?Math.round(1e3*d/f):void 0}};const jn=function(e,t){let n,r,a=0,l=1e3/t;const s=function(t){let l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();a=l,n=null,r&&(clearTimeout(r),r=null),e(...t)};return[function(){const e=Date.now(),t=e-a;for(var i=arguments.length,o=new Array(i),c=0;c<i;c++)o[c]=arguments[c];t>=l?s(o,e):(n=o,r||(r=setTimeout(()=>{r=null,s(n)},l-t)))},()=>n&&s(n)]},Nn=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const a=wn(50,250);return jn(n=>{const l=n.loaded,s=n.lengthComputable?n.total:void 0,i=l-r,o=a(i);r=l;e({loaded:l,total:s,progress:s?l/s:void 0,bytes:i,rate:o||void 0,estimated:o&&s&&l<=s?(s-l)/o:void 0,event:n,lengthComputable:null!=s,[t?"download":"upload"]:!0})},n)},kn=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Sn=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return Ft.asap(()=>e(...n))},En=an.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,an.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(an.origin),an.navigator&&/(msie|trident)/i.test(an.navigator.userAgent)):()=>!0,Cn=an.hasStandardBrowserEnv?{write(e,t,n,r,a,l){const s=[e+"="+encodeURIComponent(t)];Ft.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),Ft.isString(r)&&s.push("path="+r),Ft.isString(a)&&s.push("domain="+a),!0===l&&s.push("secure"),document.cookie=s.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function _n(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const Rn=e=>e instanceof hn?{...e}:e;function Pn(e,t){t=t||{};const n={};function r(e,t,n,r){return Ft.isPlainObject(e)&&Ft.isPlainObject(t)?Ft.merge.call({caseless:r},e,t):Ft.isPlainObject(t)?Ft.merge({},t):Ft.isArray(t)?t.slice():t}function a(e,t,n,a){return Ft.isUndefined(t)?Ft.isUndefined(e)?void 0:r(void 0,e,0,a):r(e,t,0,a)}function l(e,t){if(!Ft.isUndefined(t))return r(void 0,t)}function s(e,t){return Ft.isUndefined(t)?Ft.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function i(n,a,l){return l in t?r(n,a):l in e?r(void 0,n):void 0}const o={url:l,method:l,data:l,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:i,headers:(e,t,n)=>a(Rn(e),Rn(t),0,!0)};return Ft.forEach(Object.keys({...e,...t}),function(r){const l=o[r]||a,s=l(e[r],t[r],r);Ft.isUndefined(s)&&l!==i||(n[r]=s)}),n}const Tn=e=>{const t=Pn({},e);let n,{data:r,withXSRFToken:a,xsrfHeaderName:l,xsrfCookieName:s,headers:i,auth:o}=t;if(t.headers=i=hn.from(i),t.url=Kt(_n(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),o&&i.set("Authorization","Basic "+btoa((o.username||"")+":"+(o.password?unescape(encodeURIComponent(o.password)):""))),Ft.isFormData(r))if(an.hasStandardBrowserEnv||an.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if(!1!==(n=i.getContentType())){const[e,...t]=n?n.split(";").map(e=>e.trim()).filter(Boolean):[];i.setContentType([e||"multipart/form-data",...t].join("; "))}if(an.hasStandardBrowserEnv&&(a&&Ft.isFunction(a)&&(a=a(t)),a||!1!==a&&En(t.url))){const e=l&&s&&Cn.read(s);e&&i.set(l,e)}return t},Ln="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise(function(t,n){const r=Tn(e);let a=r.data;const l=hn.from(r.headers).normalize();let s,i,o,c,u,{responseType:d,onUploadProgress:f,onDownloadProgress:p}=r;function m(){c&&c(),u&&u(),r.cancelToken&&r.cancelToken.unsubscribe(s),r.signal&&r.signal.removeEventListener("abort",s)}let h=new XMLHttpRequest;function x(){if(!h)return;const r=hn.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders());bn(function(e){t(e),m()},function(e){n(e),m()},{data:d&&"text"!==d&&"json"!==d?h.response:h.responseText,status:h.status,statusText:h.statusText,headers:r,config:e,request:h}),h=null}h.open(r.method.toUpperCase(),r.url,!0),h.timeout=r.timeout,"onloadend"in h?h.onloadend=x:h.onreadystatechange=function(){h&&4===h.readyState&&(0!==h.status||h.responseURL&&0===h.responseURL.indexOf("file:"))&&setTimeout(x)},h.onabort=function(){h&&(n(new At("Request aborted",At.ECONNABORTED,e,h)),h=null)},h.onerror=function(){n(new At("Network Error",At.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const a=r.transitional||Xt;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new At(t,a.clarifyTimeoutError?At.ETIMEDOUT:At.ECONNABORTED,e,h)),h=null},void 0===a&&l.setContentType(null),"setRequestHeader"in h&&Ft.forEach(l.toJSON(),function(e,t){h.setRequestHeader(t,e)}),Ft.isUndefined(r.withCredentials)||(h.withCredentials=!!r.withCredentials),d&&"json"!==d&&(h.responseType=r.responseType),p&&([o,u]=Nn(p,!0),h.addEventListener("progress",o)),f&&h.upload&&([i,c]=Nn(f),h.upload.addEventListener("progress",i),h.upload.addEventListener("loadend",c)),(r.cancelToken||r.signal)&&(s=t=>{h&&(n(!t||t.type?new vn(null,e,h):t),h.abort(),h=null)},r.cancelToken&&r.cancelToken.subscribe(s),r.signal&&(r.signal.aborted?s():r.signal.addEventListener("abort",s)));const g=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);g&&-1===an.protocols.indexOf(g)?n(new At("Unsupported protocol "+g+":",At.ERR_BAD_REQUEST,e)):h.send(a||null)})},On=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const a=function(e){if(!n){n=!0,s();const t=e instanceof Error?e:this.reason;r.abort(t instanceof At?t:new vn(t instanceof Error?t.message:t))}};let l=t&&setTimeout(()=>{l=null,a(new At(`timeout ${t} of ms exceeded`,At.ETIMEDOUT))},t);const s=()=>{e&&(l&&clearTimeout(l),l=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(a):e.removeEventListener("abort",a)}),e=null)};e.forEach(e=>e.addEventListener("abort",a));const{signal:i}=r;return i.unsubscribe=()=>Ft.asap(s),i}},Fn=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,a=0;for(;a<n;)r=a+t,yield e.slice(a,r),a=r},zn=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const t=e.getReader();try{for(;;){const{done:e,value:n}=await t.read();if(e)break;yield n}}finally{await t.cancel()}},Mn=(e,t,n,r)=>{const a=async function*(e,t){for await(const n of zn(e))yield*Fn(n,t)}(e,t);let l,s=0,i=e=>{l||(l=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await a.next();if(t)return i(),void e.close();let l=r.byteLength;if(n){let e=s+=l;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw i(t),t}},cancel:e=>(i(e),a.return())},{highWaterMark:2})},Dn="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,An=Dn&&"function"===typeof ReadableStream,Un=Dn&&("function"===typeof TextEncoder?(In=new TextEncoder,e=>In.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var In;const Bn=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(aa){return!1}},$n=An&&Bn(()=>{let e=!1;const t=new Request(an.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Vn=An&&Bn(()=>Ft.isReadableStream(new Response("").body)),Hn={stream:Vn&&(e=>e.body)};var qn;Dn&&(qn=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!Hn[e]&&(Hn[e]=Ft.isFunction(qn[e])?t=>t[e]():(t,n)=>{throw new At(`Response type '${e}' is not supported`,At.ERR_NOT_SUPPORT,n)})}));const Wn=async(e,t)=>{const n=Ft.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(Ft.isBlob(e))return e.size;if(Ft.isSpecCompliantForm(e)){const t=new Request(an.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return Ft.isArrayBufferView(e)||Ft.isArrayBuffer(e)?e.byteLength:(Ft.isURLSearchParams(e)&&(e+=""),Ft.isString(e)?(await Un(e)).byteLength:void 0)})(t):n},Qn=Dn&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:l,timeout:s,onDownloadProgress:i,onUploadProgress:o,responseType:c,headers:u,withCredentials:d="same-origin",fetchOptions:f}=Tn(e);c=c?(c+"").toLowerCase():"text";let p,m=On([a,l&&l.toAbortSignal()],s);const h=m&&m.unsubscribe&&(()=>{m.unsubscribe()});let x;try{if(o&&$n&&"get"!==n&&"head"!==n&&0!==(x=await Wn(u,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(Ft.isFormData(r)&&(e=n.headers.get("content-type"))&&u.setContentType(e),n.body){const[e,t]=kn(x,Nn(Sn(o)));r=Mn(n.body,65536,e,t)}}Ft.isString(d)||(d=d?"include":"omit");const a="credentials"in Request.prototype;p=new Request(t,{...f,signal:m,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:a?d:void 0});let l=await fetch(p,f);const s=Vn&&("stream"===c||"response"===c);if(Vn&&(i||s&&h)){const e={};["status","statusText","headers"].forEach(t=>{e[t]=l[t]});const t=Ft.toFiniteNumber(l.headers.get("content-length")),[n,r]=i&&kn(t,Nn(Sn(i),!0))||[];l=new Response(Mn(l.body,65536,n,()=>{r&&r(),h&&h()}),e)}c=c||"text";let g=await Hn[Ft.findKey(Hn,c)||"text"](l,e);return!s&&h&&h(),await new Promise((t,n)=>{bn(t,n,{data:g,headers:hn.from(l.headers),status:l.status,statusText:l.statusText,config:e,request:p})})}catch(g){if(h&&h(),g&&"TypeError"===g.name&&/Load failed|fetch/i.test(g.message))throw Object.assign(new At("Network Error",At.ERR_NETWORK,e,p),{cause:g.cause||g});throw At.from(g,g&&g.code,e,p)}}),Jn={http:null,xhr:Ln,fetch:Qn};Ft.forEach(Jn,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(aa){}Object.defineProperty(e,"adapterName",{value:t})}});const Kn=e=>`- ${e}`,Gn=e=>Ft.isFunction(e)||null===e||!1===e,Xn=e=>{e=Ft.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let l=0;l<t;l++){let t;if(n=e[l],r=n,!Gn(n)&&(r=Jn[(t=String(n)).toLowerCase()],void 0===r))throw new At(`Unknown adapter '${t}'`);if(r)break;a[t||"#"+l]=r}if(!r){const e=Object.entries(a).map(e=>{let[t,n]=e;return`adapter ${t} `+(!1===n?"is not supported by the environment":"is not available in the build")});let n=t?e.length>1?"since :\n"+e.map(Kn).join("\n"):" "+Kn(e[0]):"as no adapter specified";throw new At("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function Yn(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new vn(null,e)}function Zn(e){Yn(e),e.headers=hn.from(e.headers),e.data=xn.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return Xn(e.adapter||on.adapter)(e).then(function(t){return Yn(e),t.data=xn.call(e,e.transformResponse,t),t.headers=hn.from(t.headers),t},function(t){return gn(t)||(Yn(e),t&&t.response&&(t.response.data=xn.call(e,e.transformResponse,t.response),t.response.headers=hn.from(t.response.headers))),Promise.reject(t)})}const er="1.11.0",tr={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{tr[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const nr={};tr.transitional=function(e,t,n){function r(e,t){return"[Axios v"+er+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,a,l)=>{if(!1===e)throw new At(r(a," has been removed"+(t?" in "+t:"")),At.ERR_DEPRECATED);return t&&!nr[a]&&(nr[a]=!0,console.warn(r(a," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,a,l)}},tr.spelling=function(e){return(t,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};const rr={assertOptions:function(e,t,n){if("object"!==typeof e)throw new At("options must be an object",At.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const l=r[a],s=t[l];if(s){const t=e[l],n=void 0===t||s(t,l,e);if(!0!==n)throw new At("option "+l+" must be "+n,At.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new At("Unknown option "+l,At.ERR_BAD_OPTION)}},validators:tr},ar=rr.validators;class lr{constructor(e){this.defaults=e||{},this.interceptors={request:new Gt,response:new Gt}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(aa){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=Pn(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:a}=t;void 0!==n&&rr.assertOptions(n,{silentJSONParsing:ar.transitional(ar.boolean),forcedJSONParsing:ar.transitional(ar.boolean),clarifyTimeoutError:ar.transitional(ar.boolean)},!1),null!=r&&(Ft.isFunction(r)?t.paramsSerializer={serialize:r}:rr.assertOptions(r,{encode:ar.function,serialize:ar.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),rr.assertOptions(t,{baseUrl:ar.spelling("baseURL"),withXsrfToken:ar.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let l=a&&Ft.merge(a.common,a[t.method]);a&&Ft.forEach(["delete","get","head","post","put","patch","common"],e=>{delete a[e]}),t.headers=hn.concat(l,a);const s=[];let i=!0;this.interceptors.request.forEach(function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(i=i&&e.synchronous,s.unshift(e.fulfilled,e.rejected))});const o=[];let c;this.interceptors.response.forEach(function(e){o.push(e.fulfilled,e.rejected)});let u,d=0;if(!i){const e=[Zn.bind(this),void 0];for(e.unshift(...s),e.push(...o),u=e.length,c=Promise.resolve(t);d<u;)c=c.then(e[d++],e[d++]);return c}u=s.length;let f=t;for(d=0;d<u;){const e=s[d++],t=s[d++];try{f=e(f)}catch(p){t.call(this,p);break}}try{c=Zn.call(this,f)}catch(p){return Promise.reject(p)}for(d=0,u=o.length;d<u;)c=c.then(o[d++],o[d++]);return c}getUri(e){return Kt(_n((e=Pn(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}Ft.forEach(["delete","get","head","options"],function(e){lr.prototype[e]=function(t,n){return this.request(Pn(n||{},{method:e,url:t,data:(n||{}).data}))}}),Ft.forEach(["post","put","patch"],function(e){function t(t){return function(n,r,a){return this.request(Pn(a||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}lr.prototype[e]=t(),lr.prototype[e+"Form"]=t(!0)});const sr=lr;class ir{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(e){t=e});const n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t;const r=new Promise(e=>{n.subscribe(e),t=e}).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e(function(e,r,a){n.reason||(n.reason=new vn(e,r,a),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new ir(function(t){e=t}),cancel:e}}}const or=ir;const cr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(cr).forEach(e=>{let[t,n]=e;cr[n]=t});const ur=cr;const dr=function e(t){const n=new sr(t),r=Je(sr.prototype.request,n);return Ft.extend(r,sr.prototype,n,{allOwnKeys:!0}),Ft.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(Pn(t,n))},r}(on);dr.Axios=sr,dr.CanceledError=vn,dr.CancelToken=or,dr.isCancel=gn,dr.VERSION=er,dr.toFormData=Vt,dr.AxiosError=At,dr.Cancel=dr.CanceledError,dr.all=function(e){return Promise.all(e)},dr.spread=function(e){return function(t){return e.apply(null,t)}},dr.isAxiosError=function(e){return Ft.isObject(e)&&!0===e.isAxiosError},dr.mergeConfig=Pn,dr.AxiosHeaders=hn,dr.formToJSON=e=>ln(Ft.isHTMLForm(e)?new FormData(e):e),dr.getAdapter=Xn,dr.HttpStatusCode=ur,dr.default=dr;const fr=dr.create({baseURL:"/api",headers:{"Content-Type":"application/json"}});fr.interceptors.request.use(e=>{var t;return console.log(`Making ${null===(t=e.method)||void 0===t?void 0:t.toUpperCase()} request to ${e.url}`),e},e=>Promise.reject(e)),fr.interceptors.response.use(e=>e,e=>{var t;return console.error("API Error:",(null===(t=e.response)||void 0===t?void 0:t.data)||e.message),Promise.reject(e)});const pr=e=>fr.post("/fine-tunes",e),mr=()=>fr.get("/fine-tunes"),hr=e=>fr.get(`/fine-tunes/${e}`),xr=e=>fr.get(`/fine-tunes/${e}/events`),gr=e=>fr.get(`/fine-tunes/${e}/checkpoints`),yr=e=>fr.post(`/fine-tunes/${e}/cancel`),vr=e=>fr.get("/finetune/download",{params:e}),br=()=>fr.get("/files"),wr=e=>fr.get(`/files/${e}/content`),jr=e=>fr.delete(`/files/${e}`),Nr=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"fine-tune",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;const r=new FormData;return r.append("file",e),r.append("purpose",t),n&&r.append("file_type",n),fr.post("/files/upload",r,{headers:{"Content-Type":"multipart/form-data"}})},kr=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return fr.get("/hardware",{params:e?{model:e}:{}})},Sr=()=>fr.get("/models"),Er=()=>fr.get("/batches"),Cr=e=>fr.post(`/batches/${e}/cancel`),_r=()=>{const[e,t]=(0,l.useState)([]),[n,r]=(0,l.useState)(!0),[a,s]=(0,l.useState)(null),[i,o]=(0,l.useState)({total:0,running:0,completed:0,failed:0,pending:0});(0,l.useEffect)(()=>{c()},[]);const c=async()=>{try{r(!0);const e=(await mr()).data.data||[];t(e);const n={total:e.length,running:e.filter(e=>"running"===e.status).length,completed:e.filter(e=>"completed"===e.status).length,failed:e.filter(e=>"error"===e.status).length,pending:e.filter(e=>["pending","queued"].includes(e.status)).length};o(n)}catch(e){s("Failed to fetch jobs"),console.error("Error fetching jobs:",e)}finally{r(!1)}},u=e=>{switch(e){case"completed":return(0,Ve.jsx)(qe,{className:"h-5 w-5 text-green-500"});case"running":return(0,Ve.jsx)($e,{className:"h-5 w-5 text-blue-500"});case"error":return(0,Ve.jsx)(We,{className:"h-5 w-5 text-red-500"});default:return(0,Ve.jsx)(Qe,{className:"h-5 w-5 text-yellow-500"})}},d=e=>{switch(e){case"completed":return"status-completed";case"running":return"status-running";case"error":return"status-error";default:return"status-pending"}},f=e=>{let{title:t,value:n,icon:r,color:a,description:l}=e;return(0,Ve.jsx)("div",{className:"card",children:(0,Ve.jsxs)("div",{className:"flex items-center",children:[(0,Ve.jsx)("div",{className:`p-3 rounded-lg ${a}`,children:(0,Ve.jsx)(r,{className:"h-6 w-6 text-white"})}),(0,Ve.jsxs)("div",{className:"ml-4",children:[(0,Ve.jsx)("p",{className:"text-sm font-medium text-gray-600",children:t}),(0,Ve.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:n}),l&&(0,Ve.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:l})]})]})})};return n?(0,Ve.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,Ve.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"}),(0,Ve.jsx)("span",{className:"ml-2 text-gray-600",children:"Loading dashboard..."})]}):(0,Ve.jsxs)("div",{className:"space-y-8",children:[(0,Ve.jsxs)("div",{className:"flex justify-between items-center",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard"}),(0,Ve.jsx)("p",{className:"text-gray-600 mt-1",children:"Monitor and manage your fine-tuning jobs"})]}),(0,Ve.jsxs)(Pe,{to:"/jobs/create",className:"btn-primary",children:[(0,Ve.jsx)(De,{className:"h-5 w-5 mr-2"}),"Create New Job"]})]}),a&&(0,Ve.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,Ve.jsxs)("div",{className:"flex",children:[(0,Ve.jsx)(We,{className:"h-5 w-5 text-red-400"}),(0,Ve.jsxs)("div",{className:"ml-3",children:[(0,Ve.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error"}),(0,Ve.jsx)("p",{className:"text-sm text-red-700 mt-1",children:a})]})]})}),(0,Ve.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,Ve.jsx)(f,{title:"Total Jobs",value:i.total,icon:Be,color:"bg-primary-600",description:"All fine-tuning jobs"}),(0,Ve.jsx)(f,{title:"Running",value:i.running,icon:$e,color:"bg-blue-600",description:"Currently training"}),(0,Ve.jsx)(f,{title:"Completed",value:i.completed,icon:qe,color:"bg-green-600",description:"Successfully finished"}),(0,Ve.jsx)(f,{title:"Pending",value:i.pending,icon:Qe,color:"bg-yellow-600",description:"Waiting to start"})]}),(0,Ve.jsxs)("div",{className:"card",children:[(0,Ve.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,Ve.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Recent Jobs"}),(0,Ve.jsx)(Pe,{to:"/jobs",className:"text-primary-600 hover:text-primary-700 text-sm font-medium",children:"View all jobs \u2192"})]}),0===e.length?(0,Ve.jsxs)("div",{className:"text-center py-12",children:[(0,Ve.jsx)(Ue,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,Ve.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No jobs yet"}),(0,Ve.jsx)("p",{className:"text-gray-600 mb-4",children:"Get started by creating your first fine-tuning job"}),(0,Ve.jsxs)(Pe,{to:"/jobs/create",className:"btn-primary",children:[(0,Ve.jsx)(De,{className:"h-5 w-5 mr-2"}),"Create Job"]})]}):(0,Ve.jsx)("div",{className:"overflow-hidden",children:(0,Ve.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,Ve.jsx)("thead",{className:"bg-gray-50",children:(0,Ve.jsxs)("tr",{children:[(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Job ID"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Model"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,Ve.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.slice(0,5).map(e=>(0,Ve.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Ve.jsxs)("div",{className:"flex items-center",children:[u(e.status),(0,Ve.jsx)("span",{className:"ml-2 text-sm font-medium text-gray-900",children:e.id})]})}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.model}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Ve.jsx)("span",{className:d(e.status),children:e.status})}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString()}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,Ve.jsx)(Pe,{to:`/jobs/${e.id}`,className:"text-primary-600 hover:text-primary-900",children:"View Details"})})]},e.id))})]})})]})]})},Rr=Fe("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),Pr=Fe("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),Tr=()=>{const e=re(),[t,n]=(0,l.useState)(!1),[r,a]=(0,l.useState)(null),[s,i]=(0,l.useState)(null),[o,c]=(0,l.useState)(null),[u,d]=(0,l.useState)(!1),[f,p]=(0,l.useState)([]),[m,h]=(0,l.useState)(!0),[x,g]=(0,l.useState)({training_file:"",validation_file:"",model:"meta-llama/Meta-Llama-3.1-8B-Instruct-Reference",n_epochs:1,n_checkpoints:1,n_evals:0,batch_size:"max",learning_rate:1e-5,warmup_ratio:0,max_grad_norm:1,weight_decay:0,suffix:"",wandb_project_name:"",wandb_name:"",train_on_inputs:"auto",training_method:{method:"sft",train_on_inputs:"auto"},training_type:{type:"Full"}});(0,l.useEffect)(()=>{const e=["meta-llama/Meta-Llama-3.1-8B-Instruct-Reference","meta-llama/Meta-Llama-3.1-70B-Instruct-Reference","meta-llama/Llama-2-7b-hf","meta-llama/Llama-2-13b-hf","meta-llama/Llama-2-70b-hf","mistralai/Mistral-7B-v0.1","mistralai/Mixtral-8x7B-v0.1","NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO","togethercomputer/RedPajama-INCITE-Chat-3B-v1","togethercomputer/RedPajama-INCITE-7B-Chat","WizardLM/WizardLM-13B-V1.2","teknium/OpenHermes-2.5-Mistral-7B"];(async()=>{try{var t;h(!0);const n=(null===(t=(await Sr()).data.data)||void 0===t?void 0:t.map(e=>e.id))||e;p(n.length>0?n:e)}catch(r){console.error("Error fetching models:",r),p(e)}finally{h(!1)}})()},[]);const y=e=>{const{name:t,value:n,type:r}=e.target;if(t.includes(".")){const[e,a]=t.split(".");g(t=>({...t,[e]:{...t[e],[a]:"number"===r?parseFloat(n):n}}))}else g(e=>({...e,[t]:"number"===r?parseFloat(n):n}))};return(0,Ve.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Create Fine-Tuning Job"}),(0,Ve.jsx)("p",{className:"text-gray-600 mt-1",children:"Configure and start a new fine-tuning job with your training data"})]}),r&&(0,Ve.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,Ve.jsxs)("div",{className:"flex",children:[(0,Ve.jsx)(Rr,{className:"h-5 w-5 text-red-400"}),(0,Ve.jsxs)("div",{className:"ml-3",children:[(0,Ve.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error"}),(0,Ve.jsx)("p",{className:"text-sm text-red-700 mt-1",children:r})]})]})}),s&&(0,Ve.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,Ve.jsxs)("div",{className:"flex",children:[(0,Ve.jsx)(qe,{className:"h-5 w-5 text-green-400"}),(0,Ve.jsxs)("div",{className:"ml-3",children:[(0,Ve.jsx)("h3",{className:"text-sm font-medium text-green-800",children:"Success"}),(0,Ve.jsx)("p",{className:"text-sm text-green-700 mt-1",children:s})]})]})}),(0,Ve.jsxs)("form",{onSubmit:async t=>{if(t.preventDefault(),x.training_file)try{n(!0),a(null);const t=await pr(x);i("Fine-tuning job created successfully!"),setTimeout(()=>{e(`/jobs/${t.data.id}`)},2e3)}catch(o){var r,l,s;a("Failed to create job: "+((null===(r=o.response)||void 0===r||null===(l=r.data)||void 0===l||null===(s=l.error)||void 0===s?void 0:s.message)||o.message))}finally{n(!1)}else a("Please upload a training file first")},className:"space-y-8",children:[(0,Ve.jsxs)("div",{className:"card",children:[(0,Ve.jsxs)("div",{className:"flex items-center mb-4",children:[(0,Ve.jsx)(Ue,{className:"h-6 w-6 text-primary-600 mr-2"}),(0,Ve.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Training Data"})]}),(0,Ve.jsxs)("div",{className:"space-y-4",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("label",{className:"label",children:"Training File *"}),(0,Ve.jsx)("div",{className:"mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-primary-400 transition-colors",children:(0,Ve.jsxs)("div",{className:"space-y-1 text-center",children:[(0,Ve.jsx)(Pr,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,Ve.jsxs)("div",{className:"flex text-sm text-gray-600",children:[(0,Ve.jsxs)("label",{htmlFor:"file-upload",className:"relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500",children:[(0,Ve.jsx)("span",{children:"Upload a file"}),(0,Ve.jsx)("input",{id:"file-upload",name:"file-upload",type:"file",className:"sr-only",accept:".jsonl,.txt,.json",onChange:async e=>{const t=e.target.files[0];if(t)try{d(!0),a(null);const e=t.name.split(".").pop().toLowerCase();let n="jsonl";"csv"===e?n="csv":"parquet"===e&&(n="parquet");const r=await Nr(t,"fine-tune",n);c(r.data),g(e=>({...e,training_file:r.data.id})),i("File uploaded successfully!"),setTimeout(()=>i(null),3e3)}catch(s){var n,r,l;a("Failed to upload file: "+((null===(n=s.response)||void 0===n||null===(r=n.data)||void 0===r||null===(l=r.error)||void 0===l?void 0:l.message)||s.message))}finally{d(!1)}},disabled:u})]}),(0,Ve.jsx)("p",{className:"pl-1",children:"or drag and drop"})]}),(0,Ve.jsx)("p",{className:"text-xs text-gray-500",children:"JSONL, TXT, JSON up to 10MB"})]})}),u&&(0,Ve.jsxs)("div",{className:"mt-2 flex items-center text-sm text-gray-600",children:[(0,Ve.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600 mr-2"}),"Uploading file..."]}),o&&(0,Ve.jsx)("div",{className:"mt-2 p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,Ve.jsxs)("div",{className:"flex items-center",children:[(0,Ve.jsx)(qe,{className:"h-5 w-5 text-green-500 mr-2"}),(0,Ve.jsxs)("span",{className:"text-sm text-green-800",children:[o.filename," (",(o.size/1024).toFixed(1)," KB)"]})]})})]}),(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("label",{htmlFor:"validation_file",className:"label",children:"Validation File (Optional)"}),(0,Ve.jsx)("input",{type:"text",id:"validation_file",name:"validation_file",value:x.validation_file,onChange:y,className:"input",placeholder:"file-id (optional)"})]})]})]}),(0,Ve.jsxs)("div",{className:"card",children:[(0,Ve.jsxs)("div",{className:"flex items-center mb-4",children:[(0,Ve.jsx)(Be,{className:"h-6 w-6 text-primary-600 mr-2"}),(0,Ve.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Model Configuration"})]}),(0,Ve.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("label",{htmlFor:"model",className:"label",children:"Base Model *"}),(0,Ve.jsx)("select",{id:"model",name:"model",value:x.model,onChange:y,className:"input",required:!0,disabled:m,children:m?(0,Ve.jsx)("option",{value:"",children:"Loading models..."}):f.map(e=>(0,Ve.jsx)("option",{value:e,children:e},e))})]}),(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("label",{htmlFor:"suffix",className:"label",children:"Model Suffix"}),(0,Ve.jsx)("input",{type:"text",id:"suffix",name:"suffix",value:x.suffix,onChange:y,className:"input",placeholder:"my-custom-model"})]})]})]}),(0,Ve.jsxs)("div",{className:"card",children:[(0,Ve.jsxs)("div",{className:"flex items-center mb-4",children:[(0,Ve.jsx)(Ie,{className:"h-6 w-6 text-primary-600 mr-2"}),(0,Ve.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Training Parameters"})]}),(0,Ve.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("label",{htmlFor:"n_epochs",className:"label",children:"Number of Epochs"}),(0,Ve.jsx)("input",{type:"number",id:"n_epochs",name:"n_epochs",value:x.n_epochs,onChange:y,className:"input",min:"1",max:"10"})]}),(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("label",{htmlFor:"learning_rate",className:"label",children:"Learning Rate"}),(0,Ve.jsx)("input",{type:"number",id:"learning_rate",name:"learning_rate",value:x.learning_rate,onChange:y,className:"input",step:"0.000001",min:"0.000001",max:"0.01"})]}),(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("label",{htmlFor:"batch_size",className:"label",children:"Batch Size"}),(0,Ve.jsxs)("select",{id:"batch_size",name:"batch_size",value:x.batch_size,onChange:y,className:"input",children:[(0,Ve.jsx)("option",{value:"max",children:"Auto (max)"}),(0,Ve.jsx)("option",{value:"1",children:"1"}),(0,Ve.jsx)("option",{value:"2",children:"2"}),(0,Ve.jsx)("option",{value:"4",children:"4"}),(0,Ve.jsx)("option",{value:"8",children:"8"}),(0,Ve.jsx)("option",{value:"16",children:"16"}),(0,Ve.jsx)("option",{value:"32",children:"32"})]})]}),(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("label",{htmlFor:"n_checkpoints",className:"label",children:"Checkpoints"}),(0,Ve.jsx)("input",{type:"number",id:"n_checkpoints",name:"n_checkpoints",value:x.n_checkpoints,onChange:y,className:"input",min:"1",max:"10"})]}),(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("label",{htmlFor:"warmup_ratio",className:"label",children:"Warmup Ratio"}),(0,Ve.jsx)("input",{type:"number",id:"warmup_ratio",name:"warmup_ratio",value:x.warmup_ratio,onChange:y,className:"input",step:"0.01",min:"0",max:"1"})]}),(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("label",{htmlFor:"weight_decay",className:"label",children:"Weight Decay"}),(0,Ve.jsx)("input",{type:"number",id:"weight_decay",name:"weight_decay",value:x.weight_decay,onChange:y,className:"input",step:"0.01",min:"0",max:"1"})]})]})]}),(0,Ve.jsxs)("div",{className:"card",children:[(0,Ve.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Training Method"}),(0,Ve.jsxs)("div",{className:"space-y-4",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("label",{htmlFor:"training_method.method",className:"label",children:"Method"}),(0,Ve.jsxs)("select",{id:"training_method.method",name:"training_method.method",value:x.training_method.method,onChange:y,className:"input",children:[(0,Ve.jsx)("option",{value:"sft",children:"Supervised Fine-Tuning (SFT)"}),(0,Ve.jsx)("option",{value:"dpo",children:"Direct Preference Optimization (DPO)"})]})]}),(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("label",{htmlFor:"training_type.type",className:"label",children:"Training Type"}),(0,Ve.jsxs)("select",{id:"training_type.type",name:"training_type.type",value:x.training_type.type,onChange:y,className:"input",children:[(0,Ve.jsx)("option",{value:"Full",children:"Full Fine-Tuning"}),(0,Ve.jsx)("option",{value:"Lora",children:"LoRA (Low-Rank Adaptation)"})]})]})]})]}),(0,Ve.jsxs)("div",{className:"card",children:[(0,Ve.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Weights & Biases (Optional)"}),(0,Ve.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("label",{htmlFor:"wandb_project_name",className:"label",children:"Project Name"}),(0,Ve.jsx)("input",{type:"text",id:"wandb_project_name",name:"wandb_project_name",value:x.wandb_project_name,onChange:y,className:"input",placeholder:"my-finetune-project"})]}),(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("label",{htmlFor:"wandb_name",className:"label",children:"Run Name"}),(0,Ve.jsx)("input",{type:"text",id:"wandb_name",name:"wandb_name",value:x.wandb_name,onChange:y,className:"input",placeholder:"experiment-1"})]})]})]}),(0,Ve.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,Ve.jsx)("button",{type:"button",onClick:()=>e("/jobs"),className:"btn-secondary",children:"Cancel"}),(0,Ve.jsx)("button",{type:"submit",disabled:t||!x.training_file,className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:t?(0,Ve.jsxs)(Ve.Fragment,{children:[(0,Ve.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Creating Job..."]}):(0,Ve.jsxs)(Ve.Fragment,{children:[(0,Ve.jsx)(De,{className:"h-5 w-5 mr-2"}),"Create Fine-Tuning Job"]})})]})]})]})},Lr=Fe("StopCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["rect",{width:"6",height:"6",x:"9",y:"9",key:"1wrtvo"}]]),Or=Fe("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),Fr=Fe("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),zr=Fe("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),Mr=Fe("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),Dr=Fe("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Ar=Fe("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),Ur=Fe("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]),Ir=()=>{var e;const{id:t}=function(){let{matches:e}=l.useContext(Y),t=e[e.length-1];return t?t.params:{}}(),n=re(),[r,a]=(0,l.useState)(null),[s,i]=(0,l.useState)([]),[o,c]=(0,l.useState)([]),[u,d]=(0,l.useState)(!0),[f,p]=(0,l.useState)(!1),[m,h]=(0,l.useState)(!1),[x,g]=(0,l.useState)(null),[y,v]=(0,l.useState)("overview");(0,l.useEffect)(()=>{b(),w(),j()},[t]);const b=async()=>{try{d(!0),g(null);const e=await hr(t);a(e.data)}catch(e){g("Failed to fetch job details"),console.error("Error fetching job details:",e)}finally{d(!1)}},w=async()=>{try{p(!0);const e=await xr(t);i(e.data.data||[])}catch(e){console.error("Error fetching events:",e)}finally{p(!1)}},j=async()=>{try{h(!0);const e=await gr(t);c(e.data.data||[])}catch(e){console.error("Error fetching checkpoints:",e)}finally{h(!1)}},N=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"merged";try{const r={ft_id:t,checkpoint:n};e&&(r.checkpoint_path=e);const a=await vr(r);if(a.data&&a.data.download_url)window.open(a.data.download_url,"_blank"),alert(`Download initiated for checkpoint: ${n}`);else if(a.data&&a.data.filename){const e=`/api/finetune/download?ft_id=${t}&checkpoint=${n}`;window.open(e,"_blank"),alert(`Download initiated for ${a.data.filename}`)}else alert("Download link generated successfully")}catch(s){var r,a,l;console.error("Error downloading model:",s),alert("Failed to download model: "+((null===(r=s.response)||void 0===r||null===(a=r.data)||void 0===a||null===(l=a.error)||void 0===l?void 0:l.message)||s.message))}},k=e=>{navigator.clipboard.writeText(e)},S=e=>new Date(e).toLocaleString(),E=e=>{switch(e){case"job_complete":case"training_complete":return(0,Ve.jsx)(qe,{className:"h-4 w-4 text-green-500"});case"job_error":return(0,Ve.jsx)(We,{className:"h-4 w-4 text-red-500"});case"training_start":case"job_start":return(0,Ve.jsx)($e,{className:"h-4 w-4 text-blue-500"});default:return(0,Ve.jsx)(Qe,{className:"h-4 w-4 text-gray-500"})}},C=e=>{switch(e){case"error":return"text-red-600 bg-red-50";case"warning":return"text-yellow-600 bg-yellow-50";case"info":return"text-blue-600 bg-blue-50";default:return"text-gray-600 bg-gray-50"}};return u?(0,Ve.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,Ve.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"}),(0,Ve.jsx)("span",{className:"ml-2 text-gray-600",children:"Loading job details..."})]}):x||!r?(0,Ve.jsxs)("div",{className:"text-center py-12",children:[(0,Ve.jsx)(We,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),(0,Ve.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Error Loading Job"}),(0,Ve.jsx)("p",{className:"text-gray-600 mb-4",children:x||"Job not found"}),(0,Ve.jsxs)("button",{onClick:()=>n("/jobs"),className:"btn-primary",children:[(0,Ve.jsx)(Or,{className:"h-5 w-5 mr-2"}),"Back to Jobs"]})]}):(0,Ve.jsxs)("div",{className:"space-y-6",children:[(0,Ve.jsxs)("div",{className:"flex items-center justify-between",children:[(0,Ve.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,Ve.jsxs)("button",{onClick:()=>n("/jobs"),className:"btn-secondary",children:[(0,Ve.jsx)(Or,{className:"h-5 w-5 mr-2"}),"Back"]}),(0,Ve.jsxs)("div",{children:[(0,Ve.jsxs)("div",{className:"flex items-center space-x-3",children:[(e=>{switch(e){case"completed":return(0,Ve.jsx)(qe,{className:"h-6 w-6 text-green-500"});case"running":return(0,Ve.jsx)($e,{className:"h-6 w-6 text-blue-500"});case"error":return(0,Ve.jsx)(We,{className:"h-6 w-6 text-red-500"});case"cancelled":return(0,Ve.jsx)(Lr,{className:"h-6 w-6 text-gray-500"});default:return(0,Ve.jsx)(Qe,{className:"h-6 w-6 text-yellow-500"})}})(r.status),(0,Ve.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:r.id}),(0,Ve.jsx)("span",{className:(e=>{switch(e){case"completed":return"status-completed";case"running":return"status-running";case"error":return"status-error";case"cancelled":return"status-cancelled";default:return"status-pending"}})(r.status),children:r.status})]}),(0,Ve.jsx)("p",{className:"text-gray-600 mt-1",children:"Fine-tuning job details and monitoring"})]})]}),(0,Ve.jsxs)("div",{className:"flex space-x-3",children:[(0,Ve.jsxs)("button",{onClick:()=>{b(),w(),j()},className:"btn-secondary",children:[(0,Ve.jsx)(Fr,{className:"h-5 w-5 mr-2"}),"Refresh"]}),"completed"===r.status&&(0,Ve.jsxs)("button",{onClick:()=>N(null,"merged"),className:"btn-success",children:[(0,Ve.jsx)(zr,{className:"h-5 w-5 mr-2"}),"Download Model"]}),(_=r.status,["pending","queued","running"].includes(_)&&(0,Ve.jsxs)("button",{onClick:async()=>{if(window.confirm("Are you sure you want to cancel this job?"))try{await yr(t),await b()}catch(a){var e,n,r;console.error("Error cancelling job:",a),alert("Failed to cancel job: "+((null===(e=a.response)||void 0===e||null===(n=e.data)||void 0===n||null===(r=n.error)||void 0===r?void 0:r.message)||a.message))}},className:"btn-error",children:[(0,Ve.jsx)(Lr,{className:"h-5 w-5 mr-2"}),"Cancel Job"]}))]})]}),(0,Ve.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,Ve.jsxs)("div",{className:"card",children:[(0,Ve.jsxs)("div",{className:"flex items-center justify-between",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Progress"}),(0,Ve.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[r.epochs_completed||0,"/",r.n_epochs||1]}),(0,Ve.jsx)("p",{className:"text-sm text-gray-500",children:"Epochs completed"})]}),(0,Ve.jsx)(Mr,{className:"h-8 w-8 text-primary-600"})]}),r.n_epochs&&(0,Ve.jsx)("div",{className:"mt-4",children:(0,Ve.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,Ve.jsx)("div",{className:"bg-primary-600 h-2 rounded-full transition-all duration-300",style:{width:(r.epochs_completed||0)/r.n_epochs*100+"%"}})})})]}),(0,Ve.jsx)("div",{className:"card",children:(0,Ve.jsxs)("div",{className:"flex items-center justify-between",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Token Count"}),(0,Ve.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null===(e=r.token_count)||void 0===e?void 0:e.toLocaleString())||"N/A"}),(0,Ve.jsx)("p",{className:"text-sm text-gray-500",children:"Tokens processed"})]}),(0,Ve.jsx)(Ue,{className:"h-8 w-8 text-green-600"})]})}),(0,Ve.jsx)("div",{className:"card",children:(0,Ve.jsxs)("div",{className:"flex items-center justify-between",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Cost"}),(0,Ve.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["$",(r.total_price/100||0).toFixed(2)]}),(0,Ve.jsx)("p",{className:"text-sm text-gray-500",children:"USD"})]}),(0,Ve.jsx)(Ie,{className:"h-8 w-8 text-yellow-600"})]})})]}),(0,Ve.jsx)("div",{className:"border-b border-gray-200",children:(0,Ve.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{id:"overview",name:"Overview",icon:Dr},{id:"events",name:"Events",icon:$e},{id:"checkpoints",name:"Checkpoints",icon:Ue}].map(e=>{const t=e.icon;return(0,Ve.jsxs)("button",{onClick:()=>v(e.id),className:"flex items-center py-2 px-1 border-b-2 font-medium text-sm "+(y===e.id?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,Ve.jsx)(t,{className:"h-5 w-5 mr-2"}),e.name]},e.id)})})}),"overview"===y&&(0,Ve.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,Ve.jsxs)("div",{className:"card",children:[(0,Ve.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Configuration"}),(0,Ve.jsxs)("dl",{className:"space-y-3",children:[(0,Ve.jsxs)("div",{className:"flex justify-between",children:[(0,Ve.jsx)("dt",{className:"text-sm font-medium text-gray-600",children:"Model"}),(0,Ve.jsx)("dd",{className:"text-sm text-gray-900",children:r.model})]}),(0,Ve.jsxs)("div",{className:"flex justify-between",children:[(0,Ve.jsx)("dt",{className:"text-sm font-medium text-gray-600",children:"Training File"}),(0,Ve.jsxs)("dd",{className:"text-sm text-gray-900 flex items-center",children:[r.training_file,(0,Ve.jsx)("button",{onClick:()=>k(r.training_file),className:"ml-2 text-gray-400 hover:text-gray-600",children:(0,Ve.jsx)(Ar,{className:"h-4 w-4"})})]})]}),r.validation_file&&(0,Ve.jsxs)("div",{className:"flex justify-between",children:[(0,Ve.jsx)("dt",{className:"text-sm font-medium text-gray-600",children:"Validation File"}),(0,Ve.jsx)("dd",{className:"text-sm text-gray-900",children:r.validation_file})]}),(0,Ve.jsxs)("div",{className:"flex justify-between",children:[(0,Ve.jsx)("dt",{className:"text-sm font-medium text-gray-600",children:"Epochs"}),(0,Ve.jsx)("dd",{className:"text-sm text-gray-900",children:r.n_epochs})]}),(0,Ve.jsxs)("div",{className:"flex justify-between",children:[(0,Ve.jsx)("dt",{className:"text-sm font-medium text-gray-600",children:"Learning Rate"}),(0,Ve.jsx)("dd",{className:"text-sm text-gray-900",children:r.learning_rate})]}),(0,Ve.jsxs)("div",{className:"flex justify-between",children:[(0,Ve.jsx)("dt",{className:"text-sm font-medium text-gray-600",children:"Batch Size"}),(0,Ve.jsx)("dd",{className:"text-sm text-gray-900",children:r.batch_size})]}),r.wandb_url&&(0,Ve.jsxs)("div",{className:"flex justify-between",children:[(0,Ve.jsx)("dt",{className:"text-sm font-medium text-gray-600",children:"W&B URL"}),(0,Ve.jsx)("dd",{className:"text-sm text-gray-900",children:(0,Ve.jsxs)("a",{href:r.wandb_url,target:"_blank",rel:"noopener noreferrer",className:"text-primary-600 hover:text-primary-700 flex items-center",children:["View Dashboard",(0,Ve.jsx)(Ur,{className:"h-4 w-4 ml-1"})]})})]})]})]}),(0,Ve.jsxs)("div",{className:"card",children:[(0,Ve.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Metadata"}),(0,Ve.jsxs)("dl",{className:"space-y-3",children:[(0,Ve.jsxs)("div",{className:"flex justify-between",children:[(0,Ve.jsx)("dt",{className:"text-sm font-medium text-gray-600",children:"Job ID"}),(0,Ve.jsxs)("dd",{className:"text-sm text-gray-900 flex items-center",children:[r.id,(0,Ve.jsx)("button",{onClick:()=>k(r.id),className:"ml-2 text-gray-400 hover:text-gray-600",children:(0,Ve.jsx)(Ar,{className:"h-4 w-4"})})]})]}),(0,Ve.jsxs)("div",{className:"flex justify-between",children:[(0,Ve.jsx)("dt",{className:"text-sm font-medium text-gray-600",children:"Created"}),(0,Ve.jsx)("dd",{className:"text-sm text-gray-900",children:S(r.created_at)})]}),(0,Ve.jsxs)("div",{className:"flex justify-between",children:[(0,Ve.jsx)("dt",{className:"text-sm font-medium text-gray-600",children:"Updated"}),(0,Ve.jsx)("dd",{className:"text-sm text-gray-900",children:S(r.updated_at)})]}),r.model_output_name&&(0,Ve.jsxs)("div",{className:"flex justify-between",children:[(0,Ve.jsx)("dt",{className:"text-sm font-medium text-gray-600",children:"Output Model"}),(0,Ve.jsx)("dd",{className:"text-sm text-gray-900",children:r.model_output_name})]}),r.param_count&&(0,Ve.jsxs)("div",{className:"flex justify-between",children:[(0,Ve.jsx)("dt",{className:"text-sm font-medium text-gray-600",children:"Parameters"}),(0,Ve.jsx)("dd",{className:"text-sm text-gray-900",children:r.param_count.toLocaleString()})]}),void 0!==r.queue_depth&&(0,Ve.jsxs)("div",{className:"flex justify-between",children:[(0,Ve.jsx)("dt",{className:"text-sm font-medium text-gray-600",children:"Queue Depth"}),(0,Ve.jsx)("dd",{className:"text-sm text-gray-900",children:r.queue_depth})]})]})]})]}),"events"===y&&(0,Ve.jsxs)("div",{className:"card",children:[(0,Ve.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,Ve.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Training Events"}),f&&(0,Ve.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600"})]}),0===s.length?(0,Ve.jsxs)("div",{className:"text-center py-8",children:[(0,Ve.jsx)($e,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,Ve.jsx)("p",{className:"text-gray-600",children:"No events available yet"})]}):(0,Ve.jsx)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:s.map((e,t)=>(0,Ve.jsxs)("div",{className:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[E(e.type),(0,Ve.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,Ve.jsxs)("div",{className:"flex items-center justify-between",children:[(0,Ve.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.type}),(0,Ve.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${C(e.level)}`,children:e.level||"info"})]}),(0,Ve.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.message}),(0,Ve.jsxs)("div",{className:"flex items-center justify-between mt-2 text-xs text-gray-500",children:[(0,Ve.jsx)("span",{children:S(e.created_at)}),e.step&&(0,Ve.jsxs)("span",{children:["Step: ",e.step]})]})]})]},t))})]}),"checkpoints"===y&&(0,Ve.jsxs)("div",{className:"card",children:[(0,Ve.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,Ve.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Model Checkpoints"}),m&&(0,Ve.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600"})]}),0===o.length?(0,Ve.jsxs)("div",{className:"text-center py-8",children:[(0,Ve.jsx)(Ue,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,Ve.jsx)("p",{className:"text-gray-600",children:"No checkpoints available yet"})]}):(0,Ve.jsx)("div",{className:"overflow-x-auto",children:(0,Ve.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,Ve.jsx)("thead",{className:"bg-gray-50",children:(0,Ve.jsxs)("tr",{children:[(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Step"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Path"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,Ve.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:o.map((e,t)=>(0,Ve.jsxs)("tr",{children:[(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.step}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.checkpoint_type}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:S(e.created_at)}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 max-w-xs truncate",children:e.path}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,Ve.jsx)("button",{onClick:()=>N(e.path,e.checkpoint_type),className:"text-primary-600 hover:text-primary-900",title:"Download Checkpoint",children:(0,Ve.jsx)(zr,{className:"h-4 w-4"})})})]},t))})]})})]})]});var _},Br=Fe("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),$r=Fe("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]),Vr=()=>{const[e,t]=(0,l.useState)([]),[n,r]=(0,l.useState)(!0),[a,s]=(0,l.useState)(null),[i,o]=(0,l.useState)(""),[c,u]=(0,l.useState)("all"),[d,f]=(0,l.useState)("created_at"),[p,m]=(0,l.useState)("desc");(0,l.useEffect)(()=>{h()},[]);const h=async()=>{try{r(!0),s(null);const e=await mr();t(e.data.data||[])}catch(e){s("Failed to fetch jobs"),console.error("Error fetching jobs:",e)}finally{r(!1)}},x=e=>{switch(e){case"completed":return(0,Ve.jsx)(qe,{className:"h-5 w-5 text-green-500"});case"running":return(0,Ve.jsx)($e,{className:"h-5 w-5 text-blue-500"});case"error":return(0,Ve.jsx)(We,{className:"h-5 w-5 text-red-500"});case"cancelled":return(0,Ve.jsx)(Lr,{className:"h-5 w-5 text-gray-500"});case"pending":case"queued":return(0,Ve.jsx)(Qe,{className:"h-5 w-5 text-yellow-500"});default:return(0,Ve.jsx)(Rr,{className:"h-5 w-5 text-gray-500"})}},g=e=>{switch(e){case"completed":return"status-completed";case"running":return"status-running";case"error":return"status-error";case"cancelled":return"status-cancelled";default:return"status-pending"}},y=e.filter(e=>{const t=e.id.toLowerCase().includes(i.toLowerCase())||e.model.toLowerCase().includes(i.toLowerCase()),n="all"===c||e.status===c;return t&&n}).sort((e,t)=>{let n=e[d],r=t[d];return"created_at"!==d&&"updated_at"!==d||(n=new Date(n),r=new Date(r)),"asc"===p?n>r?1:-1:n<r?1:-1}),v=(e,t)=>{if(!t)return"In progress";const n=new Date(e),r=new Date(t)-n,a=Math.floor(r/6e4),l=Math.floor(a/60);return l>0?`${l}h ${a%60}m`:`${a}m`};return n?(0,Ve.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,Ve.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"}),(0,Ve.jsx)("span",{className:"ml-2 text-gray-600",children:"Loading jobs..."})]}):(0,Ve.jsxs)("div",{className:"space-y-6",children:[(0,Ve.jsxs)("div",{className:"flex justify-between items-center",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Fine-Tuning Jobs"}),(0,Ve.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage and monitor all your fine-tuning jobs"})]}),(0,Ve.jsxs)("div",{className:"flex space-x-3",children:[(0,Ve.jsxs)("button",{onClick:h,className:"btn-secondary",disabled:n,children:[(0,Ve.jsx)(Fr,{className:"h-5 w-5 mr-2 "+(n?"animate-spin":"")}),"Refresh"]}),(0,Ve.jsxs)(Pe,{to:"/jobs/create",className:"btn-primary",children:[(0,Ve.jsx)(De,{className:"h-5 w-5 mr-2"}),"Create Job"]})]})]}),a&&(0,Ve.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,Ve.jsxs)("div",{className:"flex",children:[(0,Ve.jsx)(We,{className:"h-5 w-5 text-red-400"}),(0,Ve.jsxs)("div",{className:"ml-3",children:[(0,Ve.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error"}),(0,Ve.jsx)("p",{className:"text-sm text-red-700 mt-1",children:a})]})]})}),(0,Ve.jsx)("div",{className:"card",children:(0,Ve.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,Ve.jsx)("div",{className:"flex-1",children:(0,Ve.jsxs)("div",{className:"relative",children:[(0,Ve.jsx)(Br,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,Ve.jsx)("input",{type:"text",placeholder:"Search by job ID or model...",value:i,onChange:e=>o(e.target.value),className:"pl-10 input"})]})}),(0,Ve.jsxs)("div",{className:"flex gap-3",children:[(0,Ve.jsxs)("select",{value:c,onChange:e=>u(e.target.value),className:"input w-auto",children:[(0,Ve.jsx)("option",{value:"all",children:"All Status"}),(0,Ve.jsx)("option",{value:"pending",children:"Pending"}),(0,Ve.jsx)("option",{value:"queued",children:"Queued"}),(0,Ve.jsx)("option",{value:"running",children:"Running"}),(0,Ve.jsx)("option",{value:"completed",children:"Completed"}),(0,Ve.jsx)("option",{value:"error",children:"Error"}),(0,Ve.jsx)("option",{value:"cancelled",children:"Cancelled"})]}),(0,Ve.jsxs)("select",{value:`${d}-${p}`,onChange:e=>{const[t,n]=e.target.value.split("-");f(t),m(n)},className:"input w-auto",children:[(0,Ve.jsx)("option",{value:"created_at-desc",children:"Newest First"}),(0,Ve.jsx)("option",{value:"created_at-asc",children:"Oldest First"}),(0,Ve.jsx)("option",{value:"updated_at-desc",children:"Recently Updated"}),(0,Ve.jsx)("option",{value:"status-asc",children:"Status A-Z"})]})]})]})}),(0,Ve.jsx)("div",{className:"card p-0 overflow-hidden",children:0===y.length?(0,Ve.jsxs)("div",{className:"text-center py-12 px-6",children:[(0,Ve.jsx)($e,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,Ve.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:0===e.length?"No jobs yet":"No jobs match your filters"}),(0,Ve.jsx)("p",{className:"text-gray-600 mb-4",children:0===e.length?"Get started by creating your first fine-tuning job":"Try adjusting your search or filter criteria"}),0===e.length&&(0,Ve.jsxs)(Pe,{to:"/jobs/create",className:"btn-primary",children:[(0,Ve.jsx)(De,{className:"h-5 w-5 mr-2"}),"Create Job"]})]}):(0,Ve.jsx)("div",{className:"overflow-x-auto",children:(0,Ve.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,Ve.jsx)("thead",{className:"bg-gray-50",children:(0,Ve.jsxs)("tr",{children:[(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Job Details"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Model"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Progress"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Duration"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,Ve.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:y.map(e=>{return(0,Ve.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Ve.jsxs)("div",{className:"flex items-center",children:[x(e.status),(0,Ve.jsxs)("div",{className:"ml-3",children:[(0,Ve.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.id}),e.suffix&&(0,Ve.jsxs)("div",{className:"text-sm text-gray-500",children:["Suffix: ",e.suffix]})]})]})}),(0,Ve.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,Ve.jsx)("div",{className:"text-sm text-gray-900",children:e.model}),e.model_output_name&&(0,Ve.jsxs)("div",{className:"text-sm text-gray-500",children:["Output: ",e.model_output_name]})]}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Ve.jsx)("span",{className:g(e.status),children:e.status})}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:void 0!==e.epochs_completed&&e.n_epochs?(0,Ve.jsxs)("div",{children:[(0,Ve.jsxs)("div",{className:"text-sm text-gray-900",children:[e.epochs_completed,"/",e.n_epochs," epochs"]}),(0,Ve.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-1",children:(0,Ve.jsx)("div",{className:"bg-primary-600 h-2 rounded-full",style:{width:e.epochs_completed/e.n_epochs*100+"%"}})})]}):(0,Ve.jsx)("span",{className:"text-gray-500",children:"-"})}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:v(e.created_at,e.updated_at)}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,Ve.jsxs)("div",{className:"flex items-center",children:[(0,Ve.jsx)($r,{className:"h-4 w-4 mr-1"}),(n=e.created_at,new Date(n).toLocaleString())]})}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,Ve.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,Ve.jsx)(Pe,{to:`/jobs/${e.id}`,className:"text-primary-600 hover:text-primary-900",title:"View Details",children:(0,Ve.jsx)(Dr,{className:"h-4 w-4"})}),"completed"===e.status&&(0,Ve.jsx)("button",{onClick:()=>async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"merged";try{const n={ft_id:e,checkpoint:t},r=await vr(n);if(r.data&&r.data.download_url)window.open(r.data.download_url,"_blank"),alert(`Download initiated for job: ${e}`);else if(r.data&&r.data.filename){const n=`/api/finetune/download?ft_id=${e}&checkpoint=${t}`;window.open(n,"_blank"),alert(`Download initiated for ${r.data.filename}`)}else alert("Download link generated successfully")}catch(l){var n,r,a;console.error("Error downloading model:",l),alert("Failed to download model: "+((null===(n=l.response)||void 0===n||null===(r=n.data)||void 0===r||null===(a=r.error)||void 0===a?void 0:a.message)||l.message))}}(e.id),className:"text-green-600 hover:text-green-900",title:"Download Model",children:(0,Ve.jsx)(zr,{className:"h-4 w-4"})}),(t=e.status,["pending","queued","running"].includes(t)&&(0,Ve.jsx)("button",{onClick:()=>(async e=>{if(window.confirm("Are you sure you want to cancel this job?"))try{await yr(e),await h()}catch(a){var t,n,r;console.error("Error cancelling job:",a),alert("Failed to cancel job: "+((null===(t=a.response)||void 0===t||null===(n=t.data)||void 0===n||null===(r=n.error)||void 0===r?void 0:r.message)||a.message))}})(e.id),className:"text-red-600 hover:text-red-900",title:"Cancel Job",children:(0,Ve.jsx)(Lr,{className:"h-4 w-4"})}))]})})]},e.id);var t,n})})]})})}),e.length>0&&(0,Ve.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,Ve.jsxs)("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[(0,Ve.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:e.length}),(0,Ve.jsx)("div",{className:"text-sm text-gray-600",children:"Total Jobs"})]}),(0,Ve.jsxs)("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[(0,Ve.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:e.filter(e=>"running"===e.status).length}),(0,Ve.jsx)("div",{className:"text-sm text-gray-600",children:"Running"})]}),(0,Ve.jsxs)("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[(0,Ve.jsx)("div",{className:"text-2xl font-bold text-green-600",children:e.filter(e=>"completed"===e.status).length}),(0,Ve.jsx)("div",{className:"text-sm text-gray-600",children:"Completed"})]}),(0,Ve.jsxs)("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[(0,Ve.jsx)("div",{className:"text-2xl font-bold text-red-600",children:e.filter(e=>"error"===e.status).length}),(0,Ve.jsx)("div",{className:"text-sm text-gray-600",children:"Failed"})]})]})]})},Hr=Fe("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]),qr=Fe("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),Wr=()=>{const[e,t]=(0,l.useState)([]),[n,r]=(0,l.useState)(!0),[a,s]=(0,l.useState)(null),[i,o]=(0,l.useState)(!1),[c,u]=(0,l.useState)(null);(0,l.useEffect)(()=>{d()},[]);const d=async()=>{try{r(!0),s(null);const e=await br();t(e.data.data||[])}catch(e){s("Failed to fetch files"),console.error("Error fetching files:",e)}finally{r(!1)}},f=e=>{if(0===e)return"0 Bytes";const t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]},p=e=>{switch(e){case"csv":return(0,Ve.jsx)(Ue,{className:"h-5 w-5 text-green-600"});case"jsonl":return(0,Ve.jsx)(Ue,{className:"h-5 w-5 text-blue-600"});case"parquet":return(0,Ve.jsx)(Ue,{className:"h-5 w-5 text-purple-600"});default:return(0,Ve.jsx)(Ue,{className:"h-5 w-5 text-gray-600"})}},m=e=>{const t={"fine-tune":"bg-blue-100 text-blue-800",eval:"bg-green-100 text-green-800","eval-sample":"bg-yellow-100 text-yellow-800","eval-output":"bg-purple-100 text-purple-800","eval-summary":"bg-indigo-100 text-indigo-800","batch-generated":"bg-pink-100 text-pink-800","batch-api":"bg-gray-100 text-gray-800"};return(0,Ve.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${t[e]||t["batch-api"]}`,children:e})};return n?(0,Ve.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,Ve.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"}),(0,Ve.jsx)("span",{className:"ml-2 text-gray-600",children:"Loading files..."})]}):(0,Ve.jsxs)("div",{className:"space-y-6",children:[(0,Ve.jsxs)("div",{className:"flex justify-between items-center",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"File Management"}),(0,Ve.jsx)("p",{className:"text-gray-600 mt-1",children:"Upload and manage training data files for fine-tuning"})]}),(0,Ve.jsx)("div",{className:"flex space-x-3",children:(0,Ve.jsxs)("button",{onClick:d,className:"btn-secondary",disabled:n,children:[(0,Ve.jsx)(Fr,{className:"h-5 w-5 mr-2 "+(n?"animate-spin":"")}),"Refresh"]})})]}),a&&(0,Ve.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,Ve.jsxs)("div",{className:"flex",children:[(0,Ve.jsx)(Rr,{className:"h-5 w-5 text-red-400"}),(0,Ve.jsxs)("div",{className:"ml-3",children:[(0,Ve.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error"}),(0,Ve.jsx)("p",{className:"text-sm text-red-700 mt-1",children:a})]})]})}),c&&(0,Ve.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,Ve.jsxs)("div",{className:"flex",children:[(0,Ve.jsx)(qe,{className:"h-5 w-5 text-green-400"}),(0,Ve.jsxs)("div",{className:"ml-3",children:[(0,Ve.jsx)("h3",{className:"text-sm font-medium text-green-800",children:"Success"}),(0,Ve.jsx)("p",{className:"text-sm text-green-700 mt-1",children:c})]})]})}),(0,Ve.jsxs)("div",{className:"card",children:[(0,Ve.jsxs)("div",{className:"flex items-center mb-4",children:[(0,Ve.jsx)(Pr,{className:"h-6 w-6 text-primary-600 mr-2"}),(0,Ve.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Upload New File"})]}),(0,Ve.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-primary-400 transition-colors",children:(0,Ve.jsxs)("div",{className:"text-center",children:[(0,Ve.jsx)(Pr,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,Ve.jsxs)("div",{className:"mt-4",children:[(0,Ve.jsxs)("label",{htmlFor:"file-upload",className:"cursor-pointer",children:[(0,Ve.jsx)("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:i?"Uploading...":"Click to upload or drag and drop"}),(0,Ve.jsx)("span",{className:"mt-1 block text-sm text-gray-500",children:"JSONL, CSV, or Parquet files up to 10MB"}),(0,Ve.jsx)("input",{id:"file-upload",name:"file-upload",type:"file",className:"sr-only",accept:".jsonl,.csv,.parquet,.txt,.json",onChange:async e=>{const t=e.target.files[0];if(t)try{o(!0),s(null);const n=t.name.split(".").pop().toLowerCase();let r="jsonl";"csv"===n?r="csv":"parquet"===n&&(r="parquet");await Nr(t,"fine-tune",r);u(`File "${t.name}" uploaded successfully!`),setTimeout(()=>u(null),3e3),await d(),e.target.value=""}catch(l){var n,r,a;s("Failed to upload file: "+((null===(n=l.response)||void 0===n||null===(r=n.data)||void 0===r||null===(a=r.error)||void 0===a?void 0:a.message)||l.message))}finally{o(!1)}},disabled:i})]}),!i&&(0,Ve.jsxs)("button",{onClick:()=>document.getElementById("file-upload").click(),className:"mt-4 btn-primary",children:[(0,Ve.jsx)(De,{className:"h-5 w-5 mr-2"}),"Select File"]}),i&&(0,Ve.jsxs)("div",{className:"mt-4 flex items-center justify-center",children:[(0,Ve.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600 mr-2"}),(0,Ve.jsx)("span",{className:"text-sm text-gray-600",children:"Uploading..."})]})]})]})})]}),(0,Ve.jsxs)("div",{className:"card p-0 overflow-hidden",children:[(0,Ve.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,Ve.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Uploaded Files"})}),0===e.length?(0,Ve.jsxs)("div",{className:"text-center py-12 px-6",children:[(0,Ve.jsx)(Ue,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,Ve.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No files uploaded yet"}),(0,Ve.jsx)("p",{className:"text-gray-600 mb-4",children:"Upload your first training data file to get started"})]}):(0,Ve.jsx)("div",{className:"overflow-x-auto",children:(0,Ve.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,Ve.jsx)("thead",{className:"bg-gray-50",children:(0,Ve.jsxs)("tr",{children:[(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"File"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Purpose"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Size"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Lines"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,Ve.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>{var t,n;return(0,Ve.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Ve.jsxs)("div",{className:"flex items-center",children:[p(e.FileType),(0,Ve.jsxs)("div",{className:"ml-3",children:[(0,Ve.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.filename}),(0,Ve.jsx)("div",{className:"text-sm text-gray-500",children:e.id})]})]})}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:m(e.purpose)}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,Ve.jsxs)("div",{className:"flex items-center",children:[(0,Ve.jsx)(Hr,{className:"h-4 w-4 text-gray-400 mr-1"}),f(e.bytes)]})}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(null===(t=e.LineCount)||void 0===t?void 0:t.toLocaleString())||"N/A"}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Ve.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium "+(e.Processed?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:e.Processed?"Processed":"Processing"})}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,Ve.jsxs)("div",{className:"flex items-center",children:[(0,Ve.jsx)($r,{className:"h-4 w-4 mr-1"}),(n=e.created_at,new Date(1e3*n).toLocaleString())]})}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,Ve.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,Ve.jsx)("button",{onClick:()=>(async(e,t)=>{try{const n=await wr(e);window.open("","_blank").document.write(`\n        <html>\n          <head><title>${t} - Content</title></head>\n          <body>\n            <h1>${t}</h1>\n            <pre style="white-space: pre-wrap; font-family: monospace;">${JSON.stringify(n.data,null,2)}</pre>\n          </body>\n        </html>\n      `)}catch(l){var n,r,a;alert("Failed to load file content: "+((null===(n=l.response)||void 0===n||null===(r=n.data)||void 0===r||null===(a=r.error)||void 0===a?void 0:a.message)||l.message))}})(e.id,e.filename),className:"text-blue-600 hover:text-blue-900",title:"View Content",children:(0,Ve.jsx)(Dr,{className:"h-4 w-4"})}),(0,Ve.jsx)("button",{onClick:()=>(async(e,t)=>{if(window.confirm(`Are you sure you want to delete "${t}"?`))try{await jr(e),u(`File "${t}" deleted successfully!`),setTimeout(()=>u(null),3e3),await d()}catch(l){var n,r,a;s("Failed to delete file: "+((null===(n=l.response)||void 0===n||null===(r=n.data)||void 0===r||null===(a=r.error)||void 0===a?void 0:a.message)||l.message))}})(e.id,e.filename),className:"text-red-600 hover:text-red-900",title:"Delete File",children:(0,Ve.jsx)(qr,{className:"h-4 w-4"})})]})})]},e.id)})})]})})]}),e.length>0&&(0,Ve.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,Ve.jsxs)("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[(0,Ve.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:e.length}),(0,Ve.jsx)("div",{className:"text-sm text-gray-600",children:"Total Files"})]}),(0,Ve.jsxs)("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[(0,Ve.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:e.filter(e=>e.Processed).length}),(0,Ve.jsx)("div",{className:"text-sm text-gray-600",children:"Processed"})]}),(0,Ve.jsxs)("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[(0,Ve.jsx)("div",{className:"text-2xl font-bold text-green-600",children:f(e.reduce((e,t)=>e+t.bytes,0))}),(0,Ve.jsx)("div",{className:"text-sm text-gray-600",children:"Total Size"})]}),(0,Ve.jsxs)("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[(0,Ve.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:e.reduce((e,t)=>e+(t.LineCount||0),0).toLocaleString()}),(0,Ve.jsx)("div",{className:"text-sm text-gray-600",children:"Total Lines"})]})]})]})},Qr=Fe("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),Jr=Fe("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]),Kr=Fe("Cpu",[["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"9",y:"9",width:"6",height:"6",key:"o3kz5p"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]]),Gr=Fe("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),Xr=Fe("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]),Yr=Fe("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]),Zr=Fe("MemoryStick",[["path",{d:"M6 19v-3",key:"1nvgqn"}],["path",{d:"M10 19v-3",key:"iu8nkm"}],["path",{d:"M14 19v-3",key:"kcehxu"}],["path",{d:"M18 19v-3",key:"1vh91z"}],["path",{d:"M8 11V9",key:"63erz4"}],["path",{d:"M16 11V9",key:"fru6f3"}],["path",{d:"M12 11V9",key:"ha00sb"}],["path",{d:"M2 15h20",key:"16ne18"}],["path",{d:"M2 7a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v1.1a2 2 0 0 0 0 3.837V17a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-5.1a2 2 0 0 0 0-3.837Z",key:"lhddv3"}]]),ea=Fe("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]),ta=()=>{const[e,t]=(0,l.useState)([]),[n,r]=(0,l.useState)(!0),[a,s]=(0,l.useState)(null),[i,o]=(0,l.useState)(""),[c,u]=(0,l.useState)("all"),[d,f]=(0,l.useState)("pricing"),[p,m]=(0,l.useState)("");(0,l.useEffect)(()=>{h()},[p]);const h=async()=>{try{r(!0),s(null);const e=await kr(p||null);t(e.data.data||[])}catch(e){s("Failed to fetch hardware configurations"),console.error("Error fetching hardware:",e)}finally{r(!1)}},x=e=>{switch(e){case"available":return(0,Ve.jsx)(qe,{className:"h-5 w-5 text-green-500"});case"unavailable":return(0,Ve.jsx)(We,{className:"h-5 w-5 text-red-500"});case"insufficient":return(0,Ve.jsx)(Qr,{className:"h-5 w-5 text-yellow-500"});default:return(0,Ve.jsx)($e,{className:"h-5 w-5 text-gray-500"})}},g=e=>{switch(e){case"available":return"bg-green-100 text-green-800 border-green-200";case"unavailable":return"bg-red-100 text-red-800 border-red-200";case"insufficient":return"bg-yellow-100 text-yellow-800 border-yellow-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},y=e.filter(e=>{const t=e.id.toLowerCase().includes(i.toLowerCase())||e.specs.gpu_type.toLowerCase().includes(i.toLowerCase()),n="all"===c||e.availability&&e.availability.status===c;return t&&n}).sort((e,t)=>{switch(d){case"pricing":return e.pricing.cents_per_minute-t.pricing.cents_per_minute;case"gpu_count":return t.specs.gpu_count-e.specs.gpu_count;case"gpu_memory":return t.specs.gpu_memory-e.specs.gpu_memory;case"availability":return e.availability||t.availability?e.availability?t.availability?e.availability.status.localeCompare(t.availability.status):-1:1:0;default:return 0}}),v=e.length,b=e.filter(e=>{var t;return"available"===(null===(t=e.availability)||void 0===t?void 0:t.status)}).length,w=e.length>0?(e.reduce((e,t)=>e+t.pricing.cents_per_minute,0)/e.length/100).toFixed(2):0;return n?(0,Ve.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,Ve.jsxs)("div",{className:"text-center",children:[(0,Ve.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"}),(0,Ve.jsxs)("div",{className:"animate-pulse space-y-2",children:[(0,Ve.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mx-auto"}),(0,Ve.jsx)("div",{className:"h-3 bg-gray-200 rounded w-24 mx-auto"})]}),(0,Ve.jsx)("span",{className:"text-gray-600 mt-2 block",children:"Loading hardware configurations..."})]})}):a?(0,Ve.jsxs)("div",{className:"text-center py-12",children:[(0,Ve.jsx)(We,{className:"h-12 w-12 text-red-400 mx-auto mb-4 animate-pulse"}),(0,Ve.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Error Loading Hardware"}),(0,Ve.jsx)("p",{className:"text-gray-600 mb-4",children:a}),(0,Ve.jsxs)("button",{onClick:h,className:"btn-primary inline-flex items-center transform hover:scale-105 transition-transform duration-200",children:[(0,Ve.jsx)(Fr,{className:"h-5 w-5 mr-2"}),"Retry"]})]}):(0,Ve.jsxs)("div",{className:"space-y-6 animate-fadeIn",children:[(0,Ve.jsxs)("div",{className:"relative overflow-hidden bg-gradient-to-br from-primary-600 via-primary-700 to-purple-800 rounded-2xl p-8 text-white",children:[(0,Ve.jsx)("div",{className:"absolute inset-0 bg-black opacity-10"}),(0,Ve.jsx)("div",{className:"absolute top-0 right-0 transform translate-x-16 -translate-y-8",children:(0,Ve.jsx)("div",{className:"w-64 h-64 bg-white opacity-5 rounded-full animate-pulse"})}),(0,Ve.jsx)("div",{className:"absolute bottom-0 left-0 transform -translate-x-16 translate-y-8",children:(0,Ve.jsx)("div",{className:"w-48 h-48 bg-white opacity-5 rounded-full animate-pulse delay-1000"})}),(0,Ve.jsx)("div",{className:"relative z-10",children:(0,Ve.jsxs)("div",{className:"flex items-center justify-between",children:[(0,Ve.jsx)("div",{children:(0,Ve.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,Ve.jsx)("div",{className:"p-3 bg-white bg-opacity-20 rounded-xl backdrop-blur-sm",children:(0,Ve.jsx)(Jr,{className:"h-8 w-8 text-white"})}),(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("h1",{className:"text-3xl font-bold",children:"Hardware Configurations"}),(0,Ve.jsx)("p",{className:"text-primary-100 mt-1",children:"Explore available GPU configurations for fine-tuning"})]})]})}),(0,Ve.jsx)("div",{className:"text-right",children:(0,Ve.jsxs)("button",{onClick:h,className:"bg-white bg-opacity-20 hover:bg-opacity-30 backdrop-blur-sm text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 inline-flex items-center space-x-2",children:[(0,Ve.jsx)(Fr,{className:"h-5 w-5"}),(0,Ve.jsx)("span",{children:"Refresh"})]})})]})})]}),(0,Ve.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,Ve.jsx)("div",{className:"card group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:(0,Ve.jsxs)("div",{className:"flex items-center justify-between",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Configurations"}),(0,Ve.jsx)("p",{className:"text-3xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors duration-300",children:v}),(0,Ve.jsx)("p",{className:"text-sm text-gray-500",children:"Available options"})]}),(0,Ve.jsx)("div",{className:"p-3 bg-primary-100 rounded-xl group-hover:bg-primary-200 transition-colors duration-300",children:(0,Ve.jsx)(Kr,{className:"h-8 w-8 text-primary-600"})})]})}),(0,Ve.jsx)("div",{className:"card group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:(0,Ve.jsxs)("div",{className:"flex items-center justify-between",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Available Now"}),(0,Ve.jsx)("p",{className:"text-3xl font-bold text-green-600 group-hover:text-green-700 transition-colors duration-300",children:b}),(0,Ve.jsx)("p",{className:"text-sm text-gray-500",children:"Ready to use"})]}),(0,Ve.jsx)("div",{className:"p-3 bg-green-100 rounded-xl group-hover:bg-green-200 transition-colors duration-300",children:(0,Ve.jsx)(qe,{className:"h-8 w-8 text-green-600"})})]})}),(0,Ve.jsx)("div",{className:"card group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:(0,Ve.jsxs)("div",{className:"flex items-center justify-between",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Average Price"}),(0,Ve.jsxs)("p",{className:"text-3xl font-bold text-yellow-600 group-hover:text-yellow-700 transition-colors duration-300",children:["$",w]}),(0,Ve.jsx)("p",{className:"text-sm text-gray-500",children:"Per minute"})]}),(0,Ve.jsx)("div",{className:"p-3 bg-yellow-100 rounded-xl group-hover:bg-yellow-200 transition-colors duration-300",children:(0,Ve.jsx)(Gr,{className:"h-8 w-8 text-yellow-600"})})]})})]}),(0,Ve.jsx)("div",{className:"card",children:(0,Ve.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4",children:[(0,Ve.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 flex-1",children:[(0,Ve.jsxs)("div",{className:"relative flex-1",children:[(0,Ve.jsx)(Br,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,Ve.jsx)("input",{type:"text",placeholder:"Search hardware configurations...",value:i,onChange:e=>o(e.target.value),className:"pl-10 pr-4 py-3 w-full border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"})]}),(0,Ve.jsxs)("div",{className:"relative",children:[(0,Ve.jsx)(Xr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,Ve.jsxs)("select",{value:c,onChange:e=>u(e.target.value),className:"pl-10 pr-8 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none bg-white min-w-[150px] transition-all duration-200",children:[(0,Ve.jsx)("option",{value:"all",children:"All Status"}),(0,Ve.jsx)("option",{value:"available",children:"Available"}),(0,Ve.jsx)("option",{value:"unavailable",children:"Unavailable"}),(0,Ve.jsx)("option",{value:"insufficient",children:"Insufficient"})]})]})]}),(0,Ve.jsx)("div",{className:"flex space-x-4",children:(0,Ve.jsxs)("select",{value:d,onChange:e=>f(e.target.value),className:"px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none bg-white min-w-[150px] transition-all duration-200",children:[(0,Ve.jsx)("option",{value:"pricing",children:"Sort by Price"}),(0,Ve.jsx)("option",{value:"gpu_count",children:"Sort by GPU Count"}),(0,Ve.jsx)("option",{value:"gpu_memory",children:"Sort by Memory"}),(0,Ve.jsx)("option",{value:"availability",children:"Sort by Status"})]})})]})}),0===y.length?(0,Ve.jsxs)("div",{className:"text-center py-12",children:[(0,Ve.jsx)("div",{className:"animate-bounce mb-4",children:(0,Ve.jsx)(Yr,{className:"h-12 w-12 text-gray-400 mx-auto"})}),(0,Ve.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Hardware Found"}),(0,Ve.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search or filter criteria"})]}):(0,Ve.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:y.map((e,t)=>{return(0,Ve.jsxs)("div",{className:"card group hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 hover:scale-105 border-l-4 border-l-primary-500 animate-slideInUp",style:{animationDelay:100*t+"ms"},children:[(0,Ve.jsxs)("div",{className:"flex items-start justify-between mb-6",children:[(0,Ve.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,Ve.jsx)("div",{className:"text-3xl animate-pulse",children:(n=e.specs.gpu_type,n.includes("a100")?"\ud83d\ude80":n.includes("h100")?"\u26a1":n.includes("v100")?"\ud83d\udc8e":"\ud83d\udd27")}),(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("h3",{className:"font-bold text-lg text-gray-900 group-hover:text-primary-600 transition-colors duration-300",children:e.id.replace(/_/g," ").toUpperCase()}),(0,Ve.jsx)("p",{className:"text-sm text-gray-500",children:e.specs.gpu_type})]})]}),e.availability&&(0,Ve.jsxs)("div",{className:`px-3 py-1 rounded-full text-xs font-medium border flex items-center space-x-1 ${g(e.availability.status)} transition-all duration-300 group-hover:scale-110`,children:[x(e.availability.status),(0,Ve.jsx)("span",{className:"capitalize",children:e.availability.status})]})]}),(0,Ve.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[(0,Ve.jsxs)("div",{className:"bg-gray-50 rounded-xl p-4 group-hover:bg-primary-50 transition-colors duration-300",children:[(0,Ve.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,Ve.jsx)(Kr,{className:"h-5 w-5 text-primary-600"}),(0,Ve.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"GPU Count"})]}),(0,Ve.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.specs.gpu_count})]}),(0,Ve.jsxs)("div",{className:"bg-gray-50 rounded-xl p-4 group-hover:bg-primary-50 transition-colors duration-300",children:[(0,Ve.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,Ve.jsx)(Zr,{className:"h-5 w-5 text-green-600"}),(0,Ve.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Memory"})]}),(0,Ve.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[e.specs.gpu_memory,"GB"]})]}),(0,Ve.jsxs)("div",{className:"bg-gray-50 rounded-xl p-4 group-hover:bg-primary-50 transition-colors duration-300",children:[(0,Ve.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,Ve.jsx)(ea,{className:"h-5 w-5 text-blue-600"}),(0,Ve.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Link Type"})]}),(0,Ve.jsx)("p",{className:"text-lg font-bold text-gray-900 uppercase",children:e.specs.gpu_link})]}),(0,Ve.jsxs)("div",{className:"bg-gray-50 rounded-xl p-4 group-hover:bg-primary-50 transition-colors duration-300",children:[(0,Ve.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,Ve.jsx)(Gr,{className:"h-5 w-5 text-yellow-600"}),(0,Ve.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Price"})]}),(0,Ve.jsxs)("p",{className:"text-lg font-bold text-gray-900",children:["$",(e.pricing.cents_per_minute/100).toFixed(3),"/min"]})]})]}),(0,Ve.jsx)("div",{className:"bg-gradient-to-r from-primary-500 to-purple-600 rounded-xl p-4 text-white",children:(0,Ve.jsxs)("div",{className:"flex items-center justify-between",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("p",{className:"text-sm opacity-90",children:"Performance Score"}),(0,Ve.jsx)("p",{className:"text-2xl font-bold",children:Math.round(e.specs.gpu_count*e.specs.gpu_memory/10)})]}),(0,Ve.jsx)("div",{className:"animate-pulse",children:(0,Ve.jsx)(Mr,{className:"h-8 w-8"})})]})}),e.updated_at&&(0,Ve.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,Ve.jsxs)("p",{className:"text-xs text-gray-500 flex items-center space-x-1",children:[(0,Ve.jsx)($e,{className:"h-3 w-3"}),(0,Ve.jsxs)("span",{children:["Updated: ",new Date(e.updated_at).toLocaleString()]})]})})]},e.id);var n})})]})},na=()=>{const[e,t]=(0,l.useState)([]),[n,r]=(0,l.useState)(!0),[a,s]=(0,l.useState)(null),[i,o]=(0,l.useState)(""),[c,u]=(0,l.useState)("all");(0,l.useEffect)(()=>{d()},[]);const d=async()=>{try{r(!0),s(null);const e=await Er();t(e.data||[])}catch(e){s("Failed to fetch batch jobs"),console.error("Error fetching batches:",e)}finally{r(!1)}},f=e=>{switch(e){case"COMPLETED":return(0,Ve.jsx)(qe,{className:"h-4 w-4 text-green-500"});case"IN_PROGRESS":return(0,Ve.jsx)($e,{className:"h-4 w-4 text-blue-500"});case"VALIDATING":return(0,Ve.jsx)(Qe,{className:"h-4 w-4 text-yellow-500"});case"FAILED":return(0,Ve.jsx)(We,{className:"h-4 w-4 text-red-500"});case"EXPIRED":return(0,Ve.jsx)(Qr,{className:"h-4 w-4 text-orange-500"});case"CANCELLED":return(0,Ve.jsx)(Lr,{className:"h-4 w-4 text-gray-500"});default:return(0,Ve.jsx)(Qr,{className:"h-4 w-4 text-gray-500"})}},p=e=>{switch(e){case"COMPLETED":return"bg-green-100 text-green-800";case"IN_PROGRESS":return"bg-blue-100 text-blue-800";case"VALIDATING":return"bg-yellow-100 text-yellow-800";case"FAILED":return"bg-red-100 text-red-800";case"EXPIRED":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},m=e.filter(e=>{const t=e.id.toLowerCase().includes(i.toLowerCase())||e.model_id&&e.model_id.toLowerCase().includes(i.toLowerCase()),n="all"===c||e.status===c;return t&&n});return n?(0,Ve.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,Ve.jsxs)("div",{className:"text-center",children:[(0,Ve.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"}),(0,Ve.jsx)("span",{className:"text-gray-600",children:"Loading batch jobs..."})]})}):a?(0,Ve.jsxs)("div",{className:"text-center py-12",children:[(0,Ve.jsx)(We,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),(0,Ve.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Error Loading Batches"}),(0,Ve.jsx)("p",{className:"text-gray-600 mb-4",children:a}),(0,Ve.jsxs)("button",{onClick:d,className:"btn-primary",children:[(0,Ve.jsx)(Fr,{className:"h-5 w-5 mr-2"}),"Retry"]})]}):(0,Ve.jsxs)("div",{className:"space-y-6",children:[(0,Ve.jsxs)("div",{className:"flex items-center justify-between",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Batch Jobs"}),(0,Ve.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage your batch processing jobs"})]}),(0,Ve.jsxs)("div",{className:"flex space-x-3",children:[(0,Ve.jsxs)("button",{onClick:d,className:"btn-secondary",children:[(0,Ve.jsx)(Fr,{className:"h-5 w-5 mr-2"}),"Refresh"]}),(0,Ve.jsxs)("button",{className:"btn-primary",children:[(0,Ve.jsx)(De,{className:"h-5 w-5 mr-2"}),"Create Batch"]})]})]}),(0,Ve.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,Ve.jsx)("div",{className:"card",children:(0,Ve.jsxs)("div",{className:"flex items-center justify-between",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Batches"}),(0,Ve.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.length})]}),(0,Ve.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,Ve.jsx)($e,{className:"h-6 w-6 text-blue-600"})})]})}),(0,Ve.jsx)("div",{className:"card",children:(0,Ve.jsxs)("div",{className:"flex items-center justify-between",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Completed"}),(0,Ve.jsx)("p",{className:"text-2xl font-bold text-green-600",children:e.filter(e=>"COMPLETED"===e.status).length})]}),(0,Ve.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,Ve.jsx)(qe,{className:"h-6 w-6 text-green-600"})})]})}),(0,Ve.jsx)("div",{className:"card",children:(0,Ve.jsxs)("div",{className:"flex items-center justify-between",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"In Progress"}),(0,Ve.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:e.filter(e=>"IN_PROGRESS"===e.status).length})]}),(0,Ve.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,Ve.jsx)($e,{className:"h-6 w-6 text-blue-600"})})]})}),(0,Ve.jsx)("div",{className:"card",children:(0,Ve.jsxs)("div",{className:"flex items-center justify-between",children:[(0,Ve.jsxs)("div",{children:[(0,Ve.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Failed"}),(0,Ve.jsx)("p",{className:"text-2xl font-bold text-red-600",children:e.filter(e=>"FAILED"===e.status).length})]}),(0,Ve.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,Ve.jsx)(We,{className:"h-6 w-6 text-red-600"})})]})})]}),(0,Ve.jsx)("div",{className:"card",children:(0,Ve.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 sm:space-x-4",children:[(0,Ve.jsxs)("div",{className:"relative flex-1",children:[(0,Ve.jsx)(Br,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,Ve.jsx)("input",{type:"text",placeholder:"Search batch jobs...",value:i,onChange:e=>o(e.target.value),className:"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),(0,Ve.jsxs)("div",{className:"relative",children:[(0,Ve.jsx)(Xr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,Ve.jsxs)("select",{value:c,onChange:e=>u(e.target.value),className:"pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none bg-white min-w-[150px]",children:[(0,Ve.jsx)("option",{value:"all",children:"All Status"}),(0,Ve.jsx)("option",{value:"VALIDATING",children:"Validating"}),(0,Ve.jsx)("option",{value:"IN_PROGRESS",children:"In Progress"}),(0,Ve.jsx)("option",{value:"COMPLETED",children:"Completed"}),(0,Ve.jsx)("option",{value:"FAILED",children:"Failed"}),(0,Ve.jsx)("option",{value:"EXPIRED",children:"Expired"}),(0,Ve.jsx)("option",{value:"CANCELLED",children:"Cancelled"})]})]})]})}),0===m.length?(0,Ve.jsxs)("div",{className:"text-center py-12",children:[(0,Ve.jsx)($e,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,Ve.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Batch Jobs Found"}),(0,Ve.jsx)("p",{className:"text-gray-600",children:"Create your first batch job to get started"})]}):(0,Ve.jsx)("div",{className:"card overflow-hidden",children:(0,Ve.jsx)("div",{className:"overflow-x-auto",children:(0,Ve.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,Ve.jsx)("thead",{className:"bg-gray-50",children:(0,Ve.jsxs)("tr",{children:[(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Model"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Progress"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,Ve.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,Ve.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map(e=>(0,Ve.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,Ve.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900",children:[e.id.substring(0,8),"..."]}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.model_id?e.model_id.split("/").pop():"N/A"}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Ve.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${p(e.status)}`,children:[f(e.status),(0,Ve.jsx)("span",{className:"ml-1",children:e.status})]})}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:void 0!==e.progress?`${e.progress}%`:"N/A"}),(0,Ve.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString()}),(0,Ve.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2",children:[(0,Ve.jsx)("button",{className:"text-primary-600 hover:text-primary-900",children:(0,Ve.jsx)(Dr,{className:"h-4 w-4"})}),["IN_PROGRESS","VALIDATING"].includes(e.status)&&(0,Ve.jsx)("button",{onClick:()=>(async e=>{if(window.confirm("Are you sure you want to cancel this batch job?"))try{await Cr(e),await d()}catch(a){var t,n,r;console.error("Error cancelling batch:",a),alert("Failed to cancel batch: "+((null===(t=a.response)||void 0===t||null===(n=t.data)||void 0===n||null===(r=n.error)||void 0===r?void 0:r.message)||a.message))}})(e.id),className:"text-red-600 hover:text-red-900",children:(0,Ve.jsx)(Lr,{className:"h-4 w-4"})})]})]},e.id))})]})})})]})};const ra=function(){return(0,Ve.jsx)(Ce,{children:(0,Ve.jsx)(He,{children:(0,Ve.jsxs)(we,{children:[(0,Ve.jsx)(ve,{path:"/",element:(0,Ve.jsx)(ye,{to:"/dashboard",replace:!0})}),(0,Ve.jsx)(ve,{path:"/dashboard",element:(0,Ve.jsx)(_r,{})}),(0,Ve.jsx)(ve,{path:"/jobs",element:(0,Ve.jsx)(Vr,{})}),(0,Ve.jsx)(ve,{path:"/jobs/create",element:(0,Ve.jsx)(Tr,{})}),(0,Ve.jsx)(ve,{path:"/jobs/:id",element:(0,Ve.jsx)(Ir,{})}),(0,Ve.jsx)(ve,{path:"/batches",element:(0,Ve.jsx)(na,{})}),(0,Ve.jsx)(ve,{path:"/files",element:(0,Ve.jsx)(Wr,{})}),(0,Ve.jsx)(ve,{path:"/hardware",element:(0,Ve.jsx)(ta,{})})]})})})};i.createRoot(document.getElementById("root")).render((0,Ve.jsx)(l.StrictMode,{children:(0,Ve.jsx)(ra,{})}))})();
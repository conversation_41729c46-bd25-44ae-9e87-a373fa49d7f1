/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const Dna = createLucideIcon("Dna", [
  ["path", { d: "M2 15c6.667-6 13.333 0 20-6", key: "1pyr53" }],
  ["path", { d: "M9 22c1.798-1.998 2.518-3.995 2.807-5.993", key: "q3hbxp" }],
  ["path", { d: "M15 2c-1.798 1.998-2.518 3.995-2.807 5.993", key: "80uv8i" }],
  ["path", { d: "m17 6-2.5-2.5", key: "5cdfhj" }],
  ["path", { d: "m14 8-1-1", key: "15nbz5" }],
  ["path", { d: "m7 18 2.5 2.5", key: "16tu1a" }],
  ["path", { d: "m3.5 14.5.5.5", key: "hapbhd" }],
  ["path", { d: "m20 9 .5.5", key: "1n7z02" }],
  ["path", { d: "m6.5 12.5 1 1", key: "cs35ky" }],
  ["path", { d: "m16.5 10.5 1 1", key: "696xn5" }],
  ["path", { d: "m10 16 1.5 1.5", key: "11lckj" }]
]);

export { Dna as default };
//# sourceMappingURL=dna.js.map

{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Landmark = createLucideIcon(\"Landmark\", [[\"line\", {\n  x1: \"3\",\n  x2: \"21\",\n  y1: \"22\",\n  y2: \"22\",\n  key: \"j8o0r\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"6\",\n  y1: \"18\",\n  y2: \"11\",\n  key: \"10tf0k\"\n}], [\"line\", {\n  x1: \"10\",\n  x2: \"10\",\n  y1: \"18\",\n  y2: \"11\",\n  key: \"54lgf6\"\n}], [\"line\", {\n  x1: \"14\",\n  x2: \"14\",\n  y1: \"18\",\n  y2: \"11\",\n  key: \"380y\"\n}], [\"line\", {\n  x1: \"18\",\n  x2: \"18\",\n  y1: \"18\",\n  y2: \"11\",\n  key: \"1kevvc\"\n}], [\"polygon\", {\n  points: \"12 2 20 7 4 7\",\n  key: \"jkujk7\"\n}]]);\nexport { Landmark as default };\n//# sourceMappingURL=landmark.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
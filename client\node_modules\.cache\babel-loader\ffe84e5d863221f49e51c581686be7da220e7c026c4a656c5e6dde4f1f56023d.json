{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Waypoints = createLucideIcon(\"Waypoints\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"4.5\",\n  r: \"2.5\",\n  key: \"r5ysbb\"\n}], [\"path\", {\n  d: \"m10.2 6.3-3.9 3.9\",\n  key: \"1nzqf6\"\n}], [\"circle\", {\n  cx: \"4.5\",\n  cy: \"12\",\n  r: \"2.5\",\n  key: \"jydg6v\"\n}], [\"path\", {\n  d: \"M7 12h10\",\n  key: \"b7w52i\"\n}], [\"circle\", {\n  cx: \"19.5\",\n  cy: \"12\",\n  r: \"2.5\",\n  key: \"1piiel\"\n}], [\"path\", {\n  d: \"m13.8 17.7 3.9-3.9\",\n  key: \"1wyg1y\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"19.5\",\n  r: \"2.5\",\n  key: \"13o1pw\"\n}]]);\nexport { Waypoints as default };\n//# sourceMappingURL=waypoints.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
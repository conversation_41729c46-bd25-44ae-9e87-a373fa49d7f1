{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Layout = createLucideIcon(\"Layout\", [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"1m3agn\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"21\",\n  y1: \"9\",\n  y2: \"9\",\n  key: \"1vqk6q\"\n}], [\"line\", {\n  x1: \"9\",\n  x2: \"9\",\n  y1: \"21\",\n  y2: \"9\",\n  key: \"wpwpyp\"\n}]]);\nexport { Layout as default };\n//# sourceMappingURL=layout.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import axios from 'axios';

const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? ''
  : 'http://localhost:3001';

const api = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export const finetuneAPI = {
  // Create a new fine-tuning job
  createJob: (jobData) => api.post('/fine-tunes', jobData),
  
  // Get all fine-tuning jobs
  getJobs: () => api.get('/fine-tunes'),
  
  // Get a specific fine-tuning job
  getJob: (id) => api.get(`/fine-tunes/${id}`),
  
  // Get events for a fine-tuning job
  getJobEvents: (id) => api.get(`/fine-tunes/${id}/events`),
  
  // Get checkpoints for a fine-tuning job
  getJobCheckpoints: (id) => api.get(`/fine-tunes/${id}/checkpoints`),
  
  // Cancel a fine-tuning job
  cancelJob: (id) => api.post(`/fine-tunes/${id}/cancel`),
  
  // Download a fine-tuned model
  downloadModel: (params) => api.get('/finetune/download', { params }),
  
  // File management
  // List all files
  getFiles: () => api.get('/files'),
  
  // Get specific file details
  getFile: (id) => api.get(`/files/${id}`),
  
  // Get file content
  getFileContent: (id) => api.get(`/files/${id}/content`),
  
  // Delete file
  deleteFile: (id) => api.delete(`/files/${id}`),
  
  // Upload a training file
  uploadFile: (file, purpose = 'fine-tune', fileType = null) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('purpose', purpose);
    if (fileType) {
      formData.append('file_type', fileType);
    }
    return api.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // Get available hardware configurations
  getHardware: (model = null) => api.get('/hardware', { params: model ? { model } : {} }),
  
  
  // Batch Jobs API
  // List all batch jobs
  getBatches: () => api.get('/batches'),
  
  // Create a new batch job
  createBatch: (batchData) => api.post('/batches', batchData),
  
  // Get a specific batch job
  getBatch: (id) => api.get(`/batches/${id}`),
  
  // Cancel a batch job
  cancelBatch: (id) => api.post(`/batches/${id}/cancel`),
  
  // Health check
  healthCheck: () => api.get('/health'),
};

export default api;